'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { NAV_ITEMS, ROUTES } from '@/constants/routes'
import { ChevronDown, User, LogOut } from 'lucide-react'
import { mockUsers } from '@/services/mockData'

export function Navbar() {
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)
  const [currentUser, setCurrentUser] = useState<string>('')
  const router = useRouter()

  useEffect(() => {
    // Get current user from localStorage
    const username = localStorage.getItem('currentUser') || 'admin'
    setCurrentUser(username)
  }, [])

  const handleLogout = () => {
    // Clear authentication data
    localStorage.removeItem('currentUser')
    localStorage.removeItem('isAuthenticated')
    router.push(ROUTES.LOGIN)
  }

  // Get user display name
  const getUserDisplayName = () => {
    const user = mockUsers.find(u => u.username === currentUser)
    return user ? user.username : currentUser
  }

  return (
    <nav className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link href={ROUTES.DASHBOARD} className="flex items-center">
              <h1 className="text-2xl font-bold text-primary">深眸</h1>
            </Link>
          </div>

          {/* Navigation Menu */}
          <div className="hidden md:flex items-center space-x-8">
            {NAV_ITEMS.map((item) => (
              <div key={item.title} className="relative">
                {item.children ? (
                  <div
                    className="relative"
                    onMouseEnter={() => setActiveDropdown(item.title)}
                    onMouseLeave={() => setActiveDropdown(null)}
                  >
                    <button className="flex items-center text-gray-700 hover:text-primary px-3 py-2 text-sm font-medium">
                      {item.title}
                      <ChevronDown className="ml-1 h-4 w-4" />
                    </button>
                    
                    {activeDropdown === item.title && (
                      <div className="absolute top-full left-0 mt-1 w-48 bg-white rounded-md shadow-lg border z-50">
                        <div className="py-1">
                          {item.children.map((child) => (
                            <Link
                              key={child.title}
                              href={child.href}
                              className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            >
                              {child.title}
                            </Link>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <Link
                    href={item.href}
                    className="text-gray-700 hover:text-primary px-3 py-2 text-sm font-medium"
                  >
                    {item.title}
                  </Link>
                )}
              </div>
            ))}
          </div>

          {/* User Info */}
          <div className="flex items-center space-x-4">
            <div className="relative">
              <button
                className="flex items-center text-gray-700 hover:text-primary"
                onMouseEnter={() => setActiveDropdown('user')}
                onMouseLeave={() => setActiveDropdown(null)}
              >
                <User className="h-5 w-5 mr-2" />
                <span className="text-sm font-medium">{getUserDisplayName()}</span>
                <ChevronDown className="ml-1 h-4 w-4" />
              </button>
              
              {activeDropdown === 'user' && (
                <div className="absolute top-full right-0 mt-1 w-48 bg-white rounded-md shadow-lg border z-50">
                  <div className="py-1">
                    <Link
                      href={ROUTES.PROFILE}
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      个人资料
                    </Link>
                    <Link
                      href={ROUTES.SETTINGS}
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      设置
                    </Link>
                    <button
                      onClick={handleLogout}
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                    >
                      <LogOut className="h-4 w-4 mr-2" />
                      退出登录
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </nav>
  )
}
