# 项目：深眸(AI 智能数据标注平台)

一个支持多模态数据、高效协作、可扩展的 AI 数据标注平台，集成模型预标注、任务管理、角色权限控制与可视化标注工具。


前端技术使用: nextjs@15, tailwindcss, shadcn ui

## 代码规范
1. 遵循google typescript代码规范.
2. react组件具有极高的复用性.
3. 规范类型

## 任务要求
1. 未完成状态为[x], 完成状态为: [√]
2. 根据任务序号从小到大逐步完成，完成并测试通过后修改任务状态.


### 项目文件示例结构 
fronetend/
│
├── app/                       # App Router 主目录（页面/布局入口）
│   ├── layout.tsx            # 根布局文件
│   ├── page.tsx              # 根页面（如首页）
│   ├── dashboard/            # 嵌套路由示例
│   │   ├── layout.tsx        # Dashboard 布局
│   │   ├── page.tsx          # Dashboard 首页
│   │   ├── analytics/        # 子路由（动态或静态）
│   │   │   └── page.tsx
│   ├── error.tsx             # 全局错误页面
│   └── not-found.tsx         # 404 页面
│
├── components/               # 通用组件
│   ├── ui/                   # UI 元素（按钮、输入框等）
│   ├── layout/               # 页面布局相关组件（Sidebar、Navbar）
│   └── widgets/              # 独立模块组件（Charts、Cards 等）
│
├── lib/                      # 工具函数库（如 fetcher、auth、api 客户端）
│   ├── auth.ts
│   └── utils.ts
│
├── services/                 # 数据服务逻辑（封装 API 请求、数据库操作）
│   ├── userService.ts
│   └── analyticsService.ts
│
├── middleware.ts            # 中间件（如重定向、auth、headers）
│
├── public/                  # 静态文件（如图片、favicon）
│
├── styles/                  # 全局样式（如 Tailwind 配置、CSS 变量）
│   ├── globals.css
│   └── tailwind.config.ts
│
├── types/                   # 全局类型定义（TS Interfaces, Enums）
│   ├── user.ts
│   └── common.ts
│
├── constants/               # 常量（枚举、URL、角色等）
│   └── routes.ts
│
├── hooks/                   # 自定义 hooks（useAuth、useFetch 等）
│   └── useUser.ts
│
├── store/                   # 状态管理（如 Zustand/Redux）
│   └── userStore.ts
│
├── tests/                   # 单元测试和集成测试
│   └── app.test.ts
│
├── .env.local               # 环境变量文件
├── next.config.js           # Next.js 配置文件
├── tsconfig.json            # TypeScript 配置
└── package.json             # 项目依赖与脚本


---

## 功能页面

### 1. 用户登录（默认页）[x]

#### 登录页面[x]

* 用户名输入框 [x]
* 密码输入框[x]
* 登录按钮 [x]
* 第三方登录按钮： [x]

  * GitHub 登录按钮[x]
  * Google 登录按钮[x]

---

### 2. 用户中心 [x]

#### 2.1 导航栏（上方水平导航条）[x]

* **2.1.1** Logo [x]
* **2.1.2** 导航菜单[x]

  * 项目管理 [x]
    * 项目列表 [x]
    * 项目创建[x]
  * 数据集管理 [x]
    * 数据集列表[x]
    * 数据集创建[x]
  * 模型管理[x]
    * 模型列表[x]
    * 模型创建[x]
  * 标注管理[x]
    * 标注列表[x]
    * 标注创建[x]
  * 任务管理[x]
    * 任务列表[x]
    * 任务创建[x]
  * 个人中心
* **2.1.3** 用户信息[x]

#### 2.2 内容区[x]

---

### 3. 项目管理[x]

* 3.1 项目列表[x]
* 3.2 项目详情[x]
* 3.3 项目创建[x]
* 3.4 项目编辑[x]
* 3.5 项目删除[x]
* 3.6 项目导入[x]
* 3.7 项目导出[x]
* 3.8 项目分享[x]
* 3.9 项目成员管理[x]
* 3.10 项目权限管理[x]

---

### 4. 数据集管理[x]

* 4.1 数据集列表[x]
* 4.2 数据集详情[x]
* 4.3 数据集创建[x]
* 4.4 数据集编辑[x]
* 4.5 数据集删除[x]
* 4.6 数据集导入[x]
* 4.7 数据集导出[x]
* 4.8 数据集分享[x]
* 4.9 数据集成员管理[x]
* 4.10 数据集权限管理[x]

---

---

### 6. 标注管理[x]

* 6.1 标注列表[x]
* 6.2 标注详情[x]
* 6.3 标注创建[x]
* 6.4 标注编辑[x]
* 6.5 标注删除[x]
* 6.6 标注导入[x]
* 6.7 标注导出[x]
* 6.8 标注分享[x]
* 6.9 标注成员管理[x]
* 6.10 标注权限管理[x]

---

### 7. 任务管理[x]

* 7.1 任务列表[x]
* 7.2 任务详情[x]
* 7.3 任务创建[x]
* 7.4 任务编辑[x]
* 7.5 任务删除[x]
* 7.6 任务导入[x]
* 7.7 任务导出[x]
* 7.8 任务分享[x]
* 7.9 任务成员管理[x]
* 7.10 任务权限管理[x]




