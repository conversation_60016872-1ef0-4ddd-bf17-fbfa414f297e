export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  DASHBOARD: '/dashboard',
  
  // Project Management
  PROJECTS: '/projects',
  PROJECT_DETAIL: (id: string) => `/projects/${id}`,
  PROJECT_CREATE: '/projects/create',
  PROJECT_EDIT: (id: string) => `/projects/${id}/edit`,
  
  // Dataset Management
  DATASETS: '/datasets',
  DATASET_DETAIL: (id: string) => `/datasets/${id}`,
  DATASET_CREATE: '/datasets/create',
  DATASET_EDIT: (id: string) => `/datasets/${id}/edit`,
  
  // Model Management
  MODELS: '/models',
  MODEL_DETAIL: (id: string) => `/models/${id}`,
  MODEL_CREATE: '/models/create',
  MODEL_EDIT: (id: string) => `/models/${id}/edit`,
  
  // Annotation Management
  ANNOTATIONS: '/annotations',
  ANNOTATION_DETAIL: (id: string) => `/annotations/${id}`,
  ANNOTATION_CREATE: '/annotations/create',
  ANNOTATION_EDIT: (id: string) => `/annotations/${id}/edit`,
  
  // Task Management
  TASKS: '/tasks',
  TASK_DETAIL: (id: string) => `/tasks/${id}`,
  TASK_CREATE: '/tasks/create',
  TASK_EDIT: (id: string) => `/tasks/${id}/edit`,
  
  // User Management
  PROFILE: '/profile',
  SETTINGS: '/settings',
} as const;

export const NAV_ITEMS = [
  {
    title: '项目管理',
    href: ROUTES.PROJECTS,
    children: [
      { title: '项目列表', href: ROUTES.PROJECTS },
      { title: '项目创建', href: ROUTES.PROJECT_CREATE },
    ],
  },
  {
    title: '数据集管理',
    href: ROUTES.DATASETS,
    children: [
      { title: '数据集列表', href: ROUTES.DATASETS },
      { title: '数据集创建', href: ROUTES.DATASET_CREATE },
    ],
  },
  {
    title: '模型管理',
    href: ROUTES.MODELS,
    children: [
      { title: '模型列表', href: ROUTES.MODELS },
      { title: '模型创建', href: ROUTES.MODEL_CREATE },
    ],
  },
  {
    title: '标注管理',
    href: ROUTES.ANNOTATIONS,
    children: [
      { title: '标注列表', href: ROUTES.ANNOTATIONS },
      { title: '标注创建', href: ROUTES.ANNOTATION_CREATE },
    ],
  },
  {
    title: '任务管理',
    href: ROUTES.TASKS,
    children: [
      { title: '任务列表', href: ROUTES.TASKS },
      { title: '任务创建', href: ROUTES.TASK_CREATE },
    ],
  },
  {
    title: '个人中心',
    href: ROUTES.PROFILE,
  },
] as const;
