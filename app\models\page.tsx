'use client'

import { useState } from 'react'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { ROUTES } from '@/constants/routes'
import { Plus, Search, MoreHorizontal, Cpu, Calendar, Activity, Download } from 'lucide-react'

// Mock data
const mockModels = [
  {
    id: '1',
    name: 'ResNet-50 分类模型',
    description: '基于ResNet-50架构的图像分类模型，支持10个类别',
    type: 'classification',
    version: 'v1.2.0',
    status: 'deployed',
    accuracy: 0.94,
    projectName: '图像分类项目',
    createdAt: '2024-01-15',
    updatedAt: '2024-01-20',
  },
  {
    id: '2',
    name: 'YOLO-v8 检测模型',
    description: '用于自动驾驶场景的目标检测模型',
    type: 'detection',
    version: 'v2.1.0',
    status: 'training',
    accuracy: 0.87,
    projectName: '目标检测项目',
    createdAt: '2024-01-10',
    updatedAt: '2024-01-18',
  },
  {
    id: '3',
    name: 'Whisper 语音识别',
    description: '多语言语音识别模型，支持中英文',
    type: 'nlp',
    version: 'v1.0.0',
    status: 'ready',
    accuracy: 0.91,
    projectName: '语音识别项目',
    createdAt: '2023-12-01',
    updatedAt: '2024-01-05',
  },
]

const statusColors = {
  training: 'bg-blue-100 text-blue-800',
  ready: 'bg-green-100 text-green-800',
  deployed: 'bg-purple-100 text-purple-800',
  error: 'bg-red-100 text-red-800',
}

const statusLabels = {
  training: '训练中',
  ready: '就绪',
  deployed: '已部署',
  error: '错误',
}

const typeLabels = {
  classification: '分类',
  detection: '检测',
  segmentation: '分割',
  nlp: '自然语言',
  custom: '自定义',
}

export default function ModelsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [models] = useState(mockModels)

  const filteredModels = models.filter(model =>
    model.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    model.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">模型管理</h1>
          <p className="text-gray-600 mt-2">管理您的AI模型</p>
        </div>
        <Link href={ROUTES.MODEL_CREATE}>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            创建模型
          </Button>
        </Link>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="搜索模型..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Models Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredModels.map((model) => (
          <Card key={model.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <CardTitle className="text-lg">{model.name}</CardTitle>
                  <CardDescription className="mt-2">
                    {model.description}
                  </CardDescription>
                </div>
                <Button variant="ghost" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span
                    className={`px-2 py-1 text-xs rounded-full ${
                      statusColors[model.status as keyof typeof statusColors]
                    }`}
                  >
                    {statusLabels[model.status as keyof typeof statusLabels]}
                  </span>
                  <span className="text-xs text-gray-500">
                    {typeLabels[model.type as keyof typeof typeLabels]}
                  </span>
                </div>

                {/* Model Metrics */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">准确率</span>
                    <span className="font-medium">{Math.round(model.accuracy * 100)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-green-600 h-2 rounded-full"
                      style={{ width: `${model.accuracy * 100}%` }}
                    ></div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center text-sm text-gray-500">
                    <Cpu className="h-4 w-4 mr-2" />
                    版本：{model.version}
                  </div>
                  <div className="flex items-center text-sm text-gray-500">
                    <Activity className="h-4 w-4 mr-2" />
                    项目：{model.projectName}
                  </div>
                  <div className="flex items-center text-sm text-gray-500">
                    <Calendar className="h-4 w-4 mr-2" />
                    更新：{model.updatedAt}
                  </div>
                </div>
                
                <div className="flex space-x-2">
                  <Link href={ROUTES.MODEL_DETAIL(model.id)} className="flex-1">
                    <Button variant="outline" className="w-full">
                      查看详情
                    </Button>
                  </Link>
                  <Button variant="ghost" size="icon">
                    <Download className="h-4 w-4" />
                  </Button>
                  <Link href={ROUTES.MODEL_EDIT(model.id)}>
                    <Button variant="ghost" size="icon">
                      编辑
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredModels.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">没有找到匹配的模型</p>
        </div>
      )}
    </div>
  )
}
