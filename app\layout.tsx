import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import '@/styles/globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: '深眸 - AI 智能数据标注平台',
  description: '一个支持多模态数据、高效协作、可扩展的 AI 数据标注平台，集成模型预标注、任务管理、角色权限控制与可视化标注工具。',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN">
      <body className={inter.className}>{children}</body>
    </html>
  )
}
