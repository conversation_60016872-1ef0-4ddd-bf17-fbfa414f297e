globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/_not-found/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./app/error.tsx":{"*":{"id":"(ssr)/./app/error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/button.tsx":{"*":{"id":"(ssr)/./components/ui/button.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/card.tsx":{"*":{"id":"(ssr)/./components/ui/card.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/login/page.tsx":{"*":{"id":"(ssr)/./app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/layout/navbar.tsx":{"*":{"id":"(ssr)/./components/layout/navbar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/page.tsx":{"*":{"id":"(ssr)/./app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/projects/page.tsx":{"*":{"id":"(ssr)/./app/projects/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/projects/create/page.tsx":{"*":{"id":"(ssr)/./app/projects/create/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/profile/page.tsx":{"*":{"id":"(ssr)/./app/profile/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/annotations/page.tsx":{"*":{"id":"(ssr)/./app/annotations/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/annotations/[id]/page.tsx":{"*":{"id":"(ssr)/./app/annotations/[id]/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"F:\\deepsight\\fronetend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"F:\\deepsight\\fronetend\\styles\\globals.css":{"id":"(app-pages-browser)/./styles/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"F:\\deepsight\\fronetend\\app\\error.tsx":{"id":"(app-pages-browser)/./app/error.tsx","name":"*","chunks":["app/error","static/chunks/app/error.js"],"async":false},"F:\\deepsight\\fronetend\\components\\ui\\button.tsx":{"id":"(app-pages-browser)/./components/ui/button.tsx","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"F:\\deepsight\\fronetend\\components\\ui\\card.tsx":{"id":"(app-pages-browser)/./components/ui/card.tsx","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"F:\\deepsight\\fronetend\\node_modules\\next\\dist\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"F:\\deepsight\\fronetend\\node_modules\\next\\dist\\esm\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"F:\\deepsight\\fronetend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\deepsight\\fronetend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\deepsight\\fronetend\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\deepsight\\fronetend\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\deepsight\\fronetend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\deepsight\\fronetend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\deepsight\\fronetend\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\deepsight\\fronetend\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\deepsight\\fronetend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\deepsight\\fronetend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\deepsight\\fronetend\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\deepsight\\fronetend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\deepsight\\fronetend\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\deepsight\\fronetend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\deepsight\\fronetend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\deepsight\\fronetend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\deepsight\\fronetend\\app\\login\\page.tsx":{"id":"(app-pages-browser)/./app/login/page.tsx","name":"*","chunks":[],"async":false},"F:\\deepsight\\fronetend\\components\\layout\\navbar.tsx":{"id":"(app-pages-browser)/./components/layout/navbar.tsx","name":"*","chunks":[],"async":false},"F:\\deepsight\\fronetend\\app\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./app/dashboard/page.tsx","name":"*","chunks":[],"async":false},"F:\\deepsight\\fronetend\\app\\projects\\page.tsx":{"id":"(app-pages-browser)/./app/projects/page.tsx","name":"*","chunks":[],"async":false},"F:\\deepsight\\fronetend\\app\\projects\\create\\page.tsx":{"id":"(app-pages-browser)/./app/projects/create/page.tsx","name":"*","chunks":[],"async":false},"F:\\deepsight\\fronetend\\app\\profile\\page.tsx":{"id":"(app-pages-browser)/./app/profile/page.tsx","name":"*","chunks":[],"async":false},"F:\\deepsight\\fronetend\\app\\annotations\\page.tsx":{"id":"(app-pages-browser)/./app/annotations/page.tsx","name":"*","chunks":[],"async":false},"F:\\deepsight\\fronetend\\app\\annotations\\[id]\\page.tsx":{"id":"(app-pages-browser)/./app/annotations/[id]/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"F:\\deepsight\\fronetend\\":[],"F:\\deepsight\\fronetend\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"F:\\deepsight\\fronetend\\app\\error":[],"F:\\deepsight\\fronetend\\app\\not-found":[],"F:\\deepsight\\fronetend\\app\\page":[],"F:\\deepsight\\fronetend\\app\\_not-found\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./styles/globals.css":{"*":{"id":"(rsc)/./styles/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/error.tsx":{"*":{"id":"(rsc)/./app/error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/button.tsx":{"*":{"id":"(rsc)/./components/ui/button.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/card.tsx":{"*":{"id":"(rsc)/./components/ui/card.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/login/page.tsx":{"*":{"id":"(rsc)/./app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/layout/navbar.tsx":{"*":{"id":"(rsc)/./components/layout/navbar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/page.tsx":{"*":{"id":"(rsc)/./app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/projects/page.tsx":{"*":{"id":"(rsc)/./app/projects/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/projects/create/page.tsx":{"*":{"id":"(rsc)/./app/projects/create/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/profile/page.tsx":{"*":{"id":"(rsc)/./app/profile/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/annotations/page.tsx":{"*":{"id":"(rsc)/./app/annotations/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/annotations/[id]/page.tsx":{"*":{"id":"(rsc)/./app/annotations/[id]/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}