export interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  role: UserRole;
  createdAt: Date;
  updatedAt: Date;
}

export enum UserRole {
  ADMIN = 'admin',
  MANAGER = 'manager',
  ANNOTATOR = 'annotator',
  VIEWER = 'viewer',
}

export interface Project {
  id: string;
  name: string;
  description: string;
  status: ProjectStatus;
  createdBy: string;
  members: ProjectMember[];
  createdAt: Date;
  updatedAt: Date;
}

export enum ProjectStatus {
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  ARCHIVED = 'archived',
}

export interface ProjectMember {
  userId: string;
  role: ProjectRole;
  joinedAt: Date;
}

export enum ProjectRole {
  OWNER = 'owner',
  ADMIN = 'admin',
  MEMBER = 'member',
  VIEWER = 'viewer',
}

export interface Dataset {
  id: string;
  name: string;
  description: string;
  type: DatasetType;
  size: number;
  status: DatasetStatus;
  projectId: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum DatasetType {
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
  TEXT = 'text',
  MULTIMODAL = 'multimodal',
}

export enum DatasetStatus {
  UPLOADING = 'uploading',
  PROCESSING = 'processing',
  READY = 'ready',
  ERROR = 'error',
}

export interface Task {
  id: string;
  name: string;
  description: string;
  type: TaskType;
  status: TaskStatus;
  projectId: string;
  datasetId: string;
  assignedTo: string[];
  createdBy: string;
  dueDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export enum TaskType {
  CLASSIFICATION = 'classification',
  DETECTION = 'detection',
  SEGMENTATION = 'segmentation',
  TRANSCRIPTION = 'transcription',
  CUSTOM = 'custom',
}

export enum TaskStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  REVIEW = 'review',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export interface Annotation {
  id: string;
  taskId: string;
  dataItemId: string;
  annotatorId: string;
  data: AnnotationData;
  status: AnnotationStatus;
  createdAt: Date;
  updatedAt: Date;
}

export interface AnnotationData {
  [key: string]: any;
}

export enum AnnotationStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

export interface Model {
  id: string;
  name: string;
  description: string;
  type: ModelType;
  version: string;
  status: ModelStatus;
  projectId: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum ModelType {
  CLASSIFICATION = 'classification',
  DETECTION = 'detection',
  SEGMENTATION = 'segmentation',
  NLP = 'nlp',
  CUSTOM = 'custom',
}

export enum ModelStatus {
  TRAINING = 'training',
  READY = 'ready',
  DEPLOYED = 'deployed',
  ERROR = 'error',
}
