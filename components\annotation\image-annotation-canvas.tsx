'use client'

import { useRef, useEffect, useState, useCallback } from 'react'

export interface AnnotationShape {
  id: string
  type: 'rectangle' | 'circle' | 'polygon' | 'point'
  coordinates: number[]
  label: string
  color: string
  confidence?: number
}

interface ImageAnnotationCanvasProps {
  imageUrl: string
  imageWidth: number
  imageHeight: number
  annotations: AnnotationShape[]
  selectedAnnotation?: string | null
  currentTool: 'select' | 'rectangle' | 'circle' | 'polygon' | 'point'
  currentLabel: string
  currentColor: string
  zoom: number
  showLabels: boolean
  onAnnotationCreate: (annotation: AnnotationShape) => void
  onAnnotationUpdate: (id: string, annotation: Partial<AnnotationShape>) => void
  onAnnotationSelect: (id: string | null) => void
}

export function ImageAnnotationCanvas({
  imageUrl,
  imageWidth,
  imageHeight,
  annotations,
  selectedAnnotation,
  currentTool,
  currentLabel,
  currentColor,
  zoom,
  showLabels,
  onAnnotationCreate,
  onAnnotationUpdate,
  onAnnotationSelect,
}: ImageAnnotationCanvasProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const imageRef = useRef<HTMLImageElement>(null)
  const [isDrawing, setIsDrawing] = useState(false)
  const [startPoint, setStartPoint] = useState<{ x: number; y: number } | null>(null)
  const [currentAnnotation, setCurrentAnnotation] = useState<AnnotationShape | null>(null)

  const drawAnnotations = useCallback((ctx: CanvasRenderingContext2D) => {
    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height)
    
    // Draw image
    const image = imageRef.current
    if (image && image.complete) {
      ctx.drawImage(image, 0, 0, imageWidth, imageHeight)
    }

    // Draw annotations
    annotations.forEach((annotation) => {
      ctx.strokeStyle = annotation.color
      ctx.fillStyle = annotation.color + '30' // 30% opacity
      ctx.lineWidth = annotation.id === selectedAnnotation ? 3 : 2

      switch (annotation.type) {
        case 'rectangle':
          const [x, y, width, height] = annotation.coordinates
          ctx.strokeRect(x, y, width, height)
          ctx.fillRect(x, y, width, height)
          break

        case 'circle':
          const [cx, cy, radius] = annotation.coordinates
          ctx.beginPath()
          ctx.arc(cx, cy, radius, 0, 2 * Math.PI)
          ctx.stroke()
          ctx.fill()
          break

        case 'point':
          const [px, py] = annotation.coordinates
          ctx.beginPath()
          ctx.arc(px, py, 5, 0, 2 * Math.PI)
          ctx.fill()
          break

        case 'polygon':
          if (annotation.coordinates.length >= 6) {
            ctx.beginPath()
            ctx.moveTo(annotation.coordinates[0], annotation.coordinates[1])
            for (let i = 2; i < annotation.coordinates.length; i += 2) {
              ctx.lineTo(annotation.coordinates[i], annotation.coordinates[i + 1])
            }
            ctx.closePath()
            ctx.stroke()
            ctx.fill()
          }
          break
      }

      // Draw label
      if (showLabels && annotation.label) {
        ctx.fillStyle = annotation.color
        ctx.font = 'bold 14px Arial'
        ctx.strokeStyle = 'white'
        ctx.lineWidth = 3
        
        const labelX = annotation.coordinates[0]
        const labelY = annotation.coordinates[1] - 8
        
        // Draw text outline
        ctx.strokeText(annotation.label, labelX, labelY)
        // Draw text
        ctx.fillText(annotation.label, labelX, labelY)
      }
    })

    // Draw current annotation being created
    if (currentAnnotation) {
      ctx.strokeStyle = currentColor
      ctx.fillStyle = currentColor + '30'
      ctx.lineWidth = 2
      ctx.setLineDash([5, 5])

      switch (currentAnnotation.type) {
        case 'rectangle':
          const [x, y, width, height] = currentAnnotation.coordinates
          ctx.strokeRect(x, y, width, height)
          break

        case 'circle':
          const [cx, cy, radius] = currentAnnotation.coordinates
          ctx.beginPath()
          ctx.arc(cx, cy, radius, 0, 2 * Math.PI)
          ctx.stroke()
          break
      }

      ctx.setLineDash([])
    }
  }, [annotations, selectedAnnotation, showLabels, currentAnnotation, imageWidth, imageHeight, currentColor])

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    canvas.width = imageWidth
    canvas.height = imageHeight

    drawAnnotations(ctx)
  }, [drawAnnotations])

  const getCanvasCoordinates = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current
    if (!canvas) return { x: 0, y: 0 }

    const rect = canvas.getBoundingClientRect()
    return {
      x: (e.clientX - rect.left) / zoom,
      y: (e.clientY - rect.top) / zoom
    }
  }

  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const coords = getCanvasCoordinates(e)

    if (currentTool === 'select') {
      // Check if clicking on an existing annotation
      const clickedAnnotation = annotations.find(annotation => {
        switch (annotation.type) {
          case 'rectangle':
            const [x, y, width, height] = annotation.coordinates
            return coords.x >= x && coords.x <= x + width && 
                   coords.y >= y && coords.y <= y + height
          case 'circle':
            const [cx, cy, radius] = annotation.coordinates
            const distance = Math.sqrt((coords.x - cx) ** 2 + (coords.y - cy) ** 2)
            return distance <= radius
          case 'point':
            const [px, py] = annotation.coordinates
            const pointDistance = Math.sqrt((coords.x - px) ** 2 + (coords.y - py) ** 2)
            return pointDistance <= 10
          default:
            return false
        }
      })

      onAnnotationSelect(clickedAnnotation?.id || null)
      return
    }

    if (currentTool === 'point') {
      const newAnnotation: AnnotationShape = {
        id: Date.now().toString(),
        type: 'point',
        coordinates: [coords.x, coords.y],
        label: currentLabel,
        color: currentColor
      }
      onAnnotationCreate(newAnnotation)
      return
    }

    setIsDrawing(true)
    setStartPoint(coords)

    if (currentTool === 'rectangle') {
      setCurrentAnnotation({
        id: Date.now().toString(),
        type: 'rectangle',
        coordinates: [coords.x, coords.y, 0, 0],
        label: currentLabel,
        color: currentColor
      })
    } else if (currentTool === 'circle') {
      setCurrentAnnotation({
        id: Date.now().toString(),
        type: 'circle',
        coordinates: [coords.x, coords.y, 0],
        label: currentLabel,
        color: currentColor
      })
    }
  }

  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing || !startPoint || !currentAnnotation) return

    const coords = getCanvasCoordinates(e)

    if (currentAnnotation.type === 'rectangle') {
      const width = coords.x - startPoint.x
      const height = coords.y - startPoint.y
      setCurrentAnnotation({
        ...currentAnnotation,
        coordinates: [startPoint.x, startPoint.y, width, height]
      })
    } else if (currentAnnotation.type === 'circle') {
      const radius = Math.sqrt(
        (coords.x - startPoint.x) ** 2 + (coords.y - startPoint.y) ** 2
      )
      setCurrentAnnotation({
        ...currentAnnotation,
        coordinates: [startPoint.x, startPoint.y, radius]
      })
    }
  }

  const handleMouseUp = () => {
    if (isDrawing && currentAnnotation) {
      // Only create annotation if it has meaningful size
      const isValidAnnotation = 
        (currentAnnotation.type === 'rectangle' && 
         Math.abs(currentAnnotation.coordinates[2]) > 5 && 
         Math.abs(currentAnnotation.coordinates[3]) > 5) ||
        (currentAnnotation.type === 'circle' && 
         currentAnnotation.coordinates[2] > 5)

      if (isValidAnnotation) {
        onAnnotationCreate(currentAnnotation)
      }
    }

    setIsDrawing(false)
    setStartPoint(null)
    setCurrentAnnotation(null)
  }

  return (
    <div className="relative">
      <img
        ref={imageRef}
        src={imageUrl}
        alt="Annotation target"
        className="absolute top-0 left-0 pointer-events-none"
        style={{ width: imageWidth, height: imageHeight }}
        onLoad={() => {
          const canvas = canvasRef.current
          if (canvas) {
            const ctx = canvas.getContext('2d')
            if (ctx) drawAnnotations(ctx)
          }
        }}
      />
      <canvas
        ref={canvasRef}
        className="absolute top-0 left-0 cursor-crosshair"
        style={{ width: imageWidth, height: imageHeight }}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      />
    </div>
  )
}
