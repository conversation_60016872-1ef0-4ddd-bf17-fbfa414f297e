/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/annotations/page";
exports.ids = ["app/annotations/page"];
exports.modules = {

/***/ "(rsc)/./app/annotations/page.tsx":
/*!**********************************!*\
  !*** ./app/annotations/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"F:\\deepsight\\fronetend\\app\\annotations\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/error.tsx":
/*!***********************!*\
  !*** ./app/error.tsx ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"F:\\deepsight\\fronetend\\app\\error.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"(rsc)/./styles/globals.css\");\n\n\n\nconst metadata = {\n    title: '深眸 - AI 智能数据标注平台',\n    description: '一个支持多模态数据、高效协作、可扩展的 AI 数据标注平台，集成模型预标注、任务管理、角色权限控制与可视化标注工具。'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUlNQTtBQUZ1QjtBQUl0QixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFXVCwySkFBZTtzQkFBR0s7Ozs7Ozs7Ozs7O0FBR3pDIiwic291cmNlcyI6WyJGOlxcZGVlcHNpZ2h0XFxmcm9uZXRlbmRcXGFwcFxcbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCB7IEludGVyIH0gZnJvbSAnbmV4dC9mb250L2dvb2dsZSdcbmltcG9ydCAnQC9zdHlsZXMvZ2xvYmFscy5jc3MnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICfmt7HnnLggLSBBSSDmmbrog73mlbDmja7moIfms6jlubPlj7AnLFxuICBkZXNjcmlwdGlvbjogJ+S4gOS4quaUr+aMgeWkmuaooeaAgeaVsOaNruOAgemrmOaViOWNj+S9nOOAgeWPr+aJqeWxleeahCBBSSDmlbDmja7moIfms6jlubPlj7DvvIzpm4bmiJDmqKHlnovpooTmoIfms6jjgIHku7vliqHnrqHnkIbjgIHop5LoibLmnYPpmZDmjqfliLbkuI7lj6/op4bljJbmoIfms6jlt6XlhbfjgIInLFxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiemgtQ05cIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT57Y2hpbGRyZW59PC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbImludGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(rsc)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(rsc)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Home_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Home,Search!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Home_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Home,Search!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Home_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Home,Search!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _constants_routes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/constants/routes */ \"(rsc)/./constants/routes.ts\");\n\n\n\n\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Home_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"mx-auto h-12 w-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                            lineNumber: 12,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"mt-4 text-6xl font-bold text-gray-900\",\n                            children: \"404\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-2 text-2xl font-semibold text-gray-700\",\n                            children: \"页面未找到\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-gray-600\",\n                            children: \"很抱歉，您访问的页面不存在\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                    lineNumber: 11,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: \"可能的原因\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    children: \"请检查以下几点\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm text-gray-600 space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 网址输入错误\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                                            lineNumber: 27,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 页面已被移动或删除\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                                            lineNumber: 28,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 您没有访问权限\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                                            lineNumber: 29,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 链接已过期\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                                            lineNumber: 30,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: ()=>window.history.back(),\n                                            variant: \"outline\",\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Home_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                                                    lineNumber: 35,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"返回上页\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                                            lineNumber: 34,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: _constants_routes__WEBPACK_IMPORTED_MODULE_4__.ROUTES.DASHBOARD,\n                                            className: \"flex-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                className: \"w-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Home_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                                                        lineNumber: 40,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"返回首页\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                                                lineNumber: 39,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Button: () => (/* binding */ Button),
/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Button = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Button() from the server but Button is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"F:\\deepsight\\fronetend\\components\\ui\\button.tsx",
"Button",
);const buttonVariants = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call buttonVariants() from the server but buttonVariants is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"F:\\deepsight\\fronetend\\components\\ui\\button.tsx",
"buttonVariants",
);

/***/ }),

/***/ "(rsc)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Card: () => (/* binding */ Card),
/* harmony export */   CardContent: () => (/* binding */ CardContent),
/* harmony export */   CardDescription: () => (/* binding */ CardDescription),
/* harmony export */   CardFooter: () => (/* binding */ CardFooter),
/* harmony export */   CardHeader: () => (/* binding */ CardHeader),
/* harmony export */   CardTitle: () => (/* binding */ CardTitle)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Card = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Card() from the server but Card is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"F:\\deepsight\\fronetend\\components\\ui\\card.tsx",
"Card",
);const CardHeader = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call CardHeader() from the server but CardHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"F:\\deepsight\\fronetend\\components\\ui\\card.tsx",
"CardHeader",
);const CardFooter = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call CardFooter() from the server but CardFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"F:\\deepsight\\fronetend\\components\\ui\\card.tsx",
"CardFooter",
);const CardTitle = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call CardTitle() from the server but CardTitle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"F:\\deepsight\\fronetend\\components\\ui\\card.tsx",
"CardTitle",
);const CardDescription = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call CardDescription() from the server but CardDescription is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"F:\\deepsight\\fronetend\\components\\ui\\card.tsx",
"CardDescription",
);const CardContent = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call CardContent() from the server but CardContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"F:\\deepsight\\fronetend\\components\\ui\\card.tsx",
"CardContent",
);

/***/ }),

/***/ "(rsc)/./constants/routes.ts":
/*!*****************************!*\
  !*** ./constants/routes.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NAV_ITEMS: () => (/* binding */ NAV_ITEMS),\n/* harmony export */   ROUTES: () => (/* binding */ ROUTES)\n/* harmony export */ });\nconst ROUTES = {\n    HOME: '/',\n    LOGIN: '/login',\n    DASHBOARD: '/dashboard',\n    // Project Management\n    PROJECTS: '/projects',\n    PROJECT_DETAIL: (id)=>`/projects/${id}`,\n    PROJECT_CREATE: '/projects/create',\n    PROJECT_EDIT: (id)=>`/projects/${id}/edit`,\n    // Dataset Management\n    DATASETS: '/datasets',\n    DATASET_DETAIL: (id)=>`/datasets/${id}`,\n    DATASET_CREATE: '/datasets/create',\n    DATASET_EDIT: (id)=>`/datasets/${id}/edit`,\n    // Model Management\n    MODELS: '/models',\n    MODEL_DETAIL: (id)=>`/models/${id}`,\n    MODEL_CREATE: '/models/create',\n    MODEL_EDIT: (id)=>`/models/${id}/edit`,\n    // Annotation Management\n    ANNOTATIONS: '/annotations',\n    ANNOTATION_DETAIL: (id)=>`/annotations/${id}`,\n    ANNOTATION_CREATE: '/annotations/create',\n    ANNOTATION_EDIT: (id)=>`/annotations/${id}/edit`,\n    // Task Management\n    TASKS: '/tasks',\n    TASK_DETAIL: (id)=>`/tasks/${id}`,\n    TASK_CREATE: '/tasks/create',\n    TASK_EDIT: (id)=>`/tasks/${id}/edit`,\n    // User Management\n    PROFILE: '/profile',\n    SETTINGS: '/settings'\n};\nconst NAV_ITEMS = [\n    {\n        title: '项目管理',\n        href: ROUTES.PROJECTS,\n        children: [\n            {\n                title: '项目列表',\n                href: ROUTES.PROJECTS\n            },\n            {\n                title: '项目创建',\n                href: ROUTES.PROJECT_CREATE\n            }\n        ]\n    },\n    {\n        title: '数据集管理',\n        href: ROUTES.DATASETS,\n        children: [\n            {\n                title: '数据集列表',\n                href: ROUTES.DATASETS\n            },\n            {\n                title: '数据集创建',\n                href: ROUTES.DATASET_CREATE\n            }\n        ]\n    },\n    {\n        title: '模型管理',\n        href: ROUTES.MODELS,\n        children: [\n            {\n                title: '模型列表',\n                href: ROUTES.MODELS\n            },\n            {\n                title: '模型创建',\n                href: ROUTES.MODEL_CREATE\n            }\n        ]\n    },\n    {\n        title: '标注管理',\n        href: ROUTES.ANNOTATIONS,\n        children: [\n            {\n                title: '标注列表',\n                href: ROUTES.ANNOTATIONS\n            },\n            {\n                title: '标注创建',\n                href: ROUTES.ANNOTATION_CREATE\n            }\n        ]\n    },\n    {\n        title: '任务管理',\n        href: ROUTES.TASKS,\n        children: [\n            {\n                title: '任务列表',\n                href: ROUTES.TASKS\n            },\n            {\n                title: '任务创建',\n                href: ROUTES.TASK_CREATE\n            }\n        ]\n    },\n    {\n        title: '个人中心',\n        href: ROUTES.PROFILE\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./constants/routes.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fannotations%2Fpage&page=%2Fannotations%2Fpage&appPaths=%2Fannotations%2Fpage&pagePath=private-next-app-dir%2Fannotations%2Fpage.tsx&appDir=F%3A%5Cdeepsight%5Cfronetend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Cdeepsight%5Cfronetend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fannotations%2Fpage&page=%2Fannotations%2Fpage&appPaths=%2Fannotations%2Fpage&pagePath=private-next-app-dir%2Fannotations%2Fpage.tsx&appDir=F%3A%5Cdeepsight%5Cfronetend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Cdeepsight%5Cfronetend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/error.tsx */ \"(rsc)/./app/error.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(rsc)/./app/not-found.tsx\"));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/annotations/page.tsx */ \"(rsc)/./app/annotations/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'annotations',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"F:\\\\deepsight\\\\fronetend\\\\app\\\\layout.tsx\"],\n'error': [module1, \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\"],\n'not-found': [module2, \"F:\\\\deepsight\\\\fronetend\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/annotations/page\",\n        pathname: \"/annotations\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fannotations%2Fpage&page=%2Fannotations%2Fpage&appPaths=%2Fannotations%2Fpage&pagePath=private-next-app-dir%2Fannotations%2Fpage.tsx&appDir=F%3A%5Cdeepsight%5Cfronetend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Cdeepsight%5Cfronetend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Capp%5C%5Cannotations%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Capp%5C%5Cannotations%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/annotations/page.tsx */ \"(rsc)/./app/annotations/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNkZWVwc2lnaHQlNUMlNUNmcm9uZXRlbmQlNUMlNUNhcHAlNUMlNUNhbm5vdGF0aW9ucyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnS0FBeUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkY6XFxcXGRlZXBzaWdodFxcXFxmcm9uZXRlbmRcXFxcYXBwXFxcXGFubm90YXRpb25zXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Capp%5C%5Cannotations%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/error.tsx */ \"(rsc)/./app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNkZWVwc2lnaHQlNUMlNUNmcm9uZXRlbmQlNUMlNUNhcHAlNUMlNUNlcnJvci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBJQUE2RSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRjpcXFxcZGVlcHNpZ2h0XFxcXGZyb25ldGVuZFxcXFxhcHBcXFxcZXJyb3IudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Ccomponents%5C%5Cui%5C%5Cbutton.tsx%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Ccomponents%5C%5Cui%5C%5Ccard.tsx%22%2C%22ids%22%3A%5B%22Card%22%2C%22CardHeader%22%2C%22CardTitle%22%2C%22CardDescription%22%2C%22CardContent%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Ccomponents%5C%5Cui%5C%5Cbutton.tsx%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Ccomponents%5C%5Cui%5C%5Ccard.tsx%22%2C%22ids%22%3A%5B%22Card%22%2C%22CardHeader%22%2C%22CardTitle%22%2C%22CardDescription%22%2C%22CardContent%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/button.tsx */ \"(rsc)/./components/ui/button.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/card.tsx */ \"(rsc)/./components/ui/card.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNkZWVwc2lnaHQlNUMlNUNmcm9uZXRlbmQlNUMlNUNjb21wb25lbnRzJTVDJTVDdWklNUMlNUNidXR0b24udHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQnV0dG9uJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNkZWVwc2lnaHQlNUMlNUNmcm9uZXRlbmQlNUMlNUNjb21wb25lbnRzJTVDJTVDdWklNUMlNUNjYXJkLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkNhcmQlMjIlMkMlMjJDYXJkSGVhZGVyJTIyJTJDJTIyQ2FyZFRpdGxlJTIyJTJDJTIyQ2FyZERlc2NyaXB0aW9uJTIyJTJDJTIyQ2FyZENvbnRlbnQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRiUzQSU1QyU1Q2RlZXBzaWdodCU1QyU1Q2Zyb25ldGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDYXBwLWRpciU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0tBQXFIO0FBQ3JIO0FBQ0EsNEpBQTBLO0FBQzFLO0FBQ0EsZ05BQTJKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJCdXR0b25cIl0gKi8gXCJGOlxcXFxkZWVwc2lnaHRcXFxcZnJvbmV0ZW5kXFxcXGNvbXBvbmVudHNcXFxcdWlcXFxcYnV0dG9uLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQ2FyZFwiLFwiQ2FyZEhlYWRlclwiLFwiQ2FyZFRpdGxlXCIsXCJDYXJkRGVzY3JpcHRpb25cIixcIkNhcmRDb250ZW50XCJdICovIFwiRjpcXFxcZGVlcHNpZ2h0XFxcXGZyb25ldGVuZFxcXFxjb21wb25lbnRzXFxcXHVpXFxcXGNhcmQudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJfX2VzTW9kdWxlXCIsXCJkZWZhdWx0XCJdICovIFwiRjpcXFxcZGVlcHNpZ2h0XFxcXGZyb25ldGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxhcHAtZGlyXFxcXGxpbmsuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Ccomponents%5C%5Cui%5C%5Cbutton.tsx%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Ccomponents%5C%5Cui%5C%5Ccard.tsx%22%2C%22ids%22%3A%5B%22Card%22%2C%22CardHeader%22%2C%22CardTitle%22%2C%22CardDescription%22%2C%22CardContent%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e36c26631245\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkY6XFxkZWVwc2lnaHRcXGZyb25ldGVuZFxcc3R5bGVzXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImUzNmMyNjYzMTI0NVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./styles/globals.css\n");

/***/ }),

/***/ "(ssr)/./app/annotations/page.tsx":
/*!**********************************!*\
  !*** ./app/annotations/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnnotationsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _constants_routes__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/constants/routes */ \"(ssr)/./constants/routes.ts\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_MoreHorizontal_Plus_Search_Tag_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,MoreHorizontal,Plus,Search,Tag,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_MoreHorizontal_Plus_Search_Tag_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,MoreHorizontal,Plus,Search,Tag,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_MoreHorizontal_Plus_Search_Tag_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,MoreHorizontal,Plus,Search,Tag,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_MoreHorizontal_Plus_Search_Tag_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,MoreHorizontal,Plus,Search,Tag,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_MoreHorizontal_Plus_Search_Tag_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,MoreHorizontal,Plus,Search,Tag,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_MoreHorizontal_Plus_Search_Tag_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,MoreHorizontal,Plus,Search,Tag,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_MoreHorizontal_Plus_Search_Tag_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,MoreHorizontal,Plus,Search,Tag,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _services_mockData__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/services/mockData */ \"(ssr)/./services/mockData.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nconst statusColors = {\n    DRAFT: 'bg-gray-100 text-gray-800',\n    SUBMITTED: 'bg-blue-100 text-blue-800',\n    APPROVED: 'bg-green-100 text-green-800',\n    REJECTED: 'bg-red-100 text-red-800'\n};\nconst statusLabels = {\n    DRAFT: '草稿',\n    SUBMITTED: '已提交',\n    APPROVED: '已批准',\n    REJECTED: '已拒绝'\n};\nfunction AnnotationsPage() {\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [annotations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_services_mockData__WEBPACK_IMPORTED_MODULE_7__.mockAnnotations);\n    const filteredAnnotations = annotations.filter((annotation)=>{\n        const task = (0,_services_mockData__WEBPACK_IMPORTED_MODULE_7__.getTaskById)(annotation.taskId);\n        const annotator = (0,_services_mockData__WEBPACK_IMPORTED_MODULE_7__.getUserById)(annotation.annotatorId);\n        return task?.name.toLowerCase().includes(searchTerm.toLowerCase()) || annotation.dataItemId.toLowerCase().includes(searchTerm.toLowerCase()) || annotator?.username.toLowerCase().includes(searchTerm.toLowerCase());\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"标注管理\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-2\",\n                                children: \"管理您的数据标注结果\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: _constants_routes__WEBPACK_IMPORTED_MODULE_6__.ROUTES.ANNOTATION_CREATE,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_MoreHorizontal_Plus_Search_Tag_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, this),\n                                \"创建标注\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative flex-1 max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_MoreHorizontal_Plus_Search_Tag_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                            placeholder: \"搜索标注...\",\n                            value: searchTerm,\n                            onChange: (e)=>setSearchTerm(e.target.value),\n                            className: \"pl-10\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                children: filteredAnnotations.map((annotation)=>{\n                    const task = (0,_services_mockData__WEBPACK_IMPORTED_MODULE_7__.getTaskById)(annotation.taskId);\n                    const annotator = (0,_services_mockData__WEBPACK_IMPORTED_MODULE_7__.getUserById)(annotation.annotatorId);\n                    // Extract labels from annotation data\n                    const getLabels = ()=>{\n                        if (annotation.data.category) return [\n                            annotation.data.category\n                        ];\n                        if (annotation.data.objects) return annotation.data.objects.map((obj)=>obj.class);\n                        if (annotation.data.sentiment) return [\n                            annotation.data.sentiment\n                        ];\n                        if (annotation.data.transcription) return [\n                            '转录文本'\n                        ];\n                        return [\n                            '未知'\n                        ];\n                    };\n                    // Get confidence from annotation data\n                    const getConfidence = ()=>{\n                        if (annotation.data.confidence) return annotation.data.confidence;\n                        if (annotation.data.objects) {\n                            const confidences = annotation.data.objects.map((obj)=>obj.confidence);\n                            return confidences.reduce((a, b)=>a + b, 0) / confidences.length;\n                        }\n                        return 0.8 // default\n                        ;\n                    };\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"hover:shadow-md transition-shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"text-lg\",\n                                                    children: annotation.dataItemId\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                    className: \"mt-2\",\n                                                    children: [\n                                                        \"任务：\",\n                                                        task?.name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_MoreHorizontal_Plus_Search_Tag_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `px-2 py-1 text-xs rounded-full ${statusColors[annotation.status]}`,\n                                                    children: statusLabels[annotation.status]\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-sm text-gray-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_MoreHorizontal_Plus_Search_Tag_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                            lineNumber: 120,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        Math.round(getConfidence() * 100),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-sm text-gray-700 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_MoreHorizontal_Plus_Search_Tag_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"标签\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-1\",\n                                                    children: getLabels().map((label, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded\",\n                                                            children: label\n                                                        }, index, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                            lineNumber: 133,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-sm text-gray-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_MoreHorizontal_Plus_Search_Tag_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                            lineNumber: 145,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"标注员：\",\n                                                        annotator?.username\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-sm text-gray-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_MoreHorizontal_Plus_Search_Tag_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"更新：\",\n                                                        annotation.updatedAt.toLocaleDateString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: _constants_routes__WEBPACK_IMPORTED_MODULE_6__.ROUTES.ANNOTATION_DETAIL(annotation.id),\n                                                    className: \"flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"outline\",\n                                                        className: \"w-full\",\n                                                        children: \"查看详情\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: _constants_routes__WEBPACK_IMPORTED_MODULE_6__.ROUTES.ANNOTATION_EDIT(annotation.id),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"icon\",\n                                                        children: \"编辑\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, annotation.id, true, {\n                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n            filteredAnnotations.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500\",\n                    children: \"没有找到匹配的标注\"\n                }, void 0, false, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n                lineNumber: 174,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\annotations\\\\page.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/annotations/page.tsx\n");

/***/ }),

/***/ "(ssr)/./app/error.tsx":
/*!***********************!*\
  !*** ./app/error.tsx ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Error)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Error({ error, reset }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Error.useEffect\": ()=>{\n            // Log the error to an error reporting service\n            console.error(error);\n        }\n    }[\"Error.useEffect\"], [\n        error\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"mx-auto h-12 w-12 text-red-500\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"mt-4 text-3xl font-bold text-gray-900\",\n                            children: \"出错了\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-gray-600\",\n                            children: \"很抱歉，页面遇到了一些问题\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: \"错误详情\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    children: error.message || '发生了未知错误'\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: reset,\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n                                                lineNumber: 39,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"重试\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>window.location.href = '/dashboard',\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"返回首页\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\app\\\\error.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/error.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Button,buttonVariants auto */ \n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 48,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Card,CardHeader,CardFooter,CardTitle,CardDescription,CardContent auto */ \n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 11,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 26,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 38,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 53,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 65,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 73,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Input auto */ \n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\deepsight\\\\fronetend\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 13,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBRUU7QUFLaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLGdXQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJGOlxcZGVlcHNpZ2h0XFxmcm9uZXRlbmRcXGNvbXBvbmVudHNcXHVpXFxpbnB1dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5leHBvcnQgaW50ZXJmYWNlIElucHV0UHJvcHNcbiAgZXh0ZW5kcyBSZWFjdC5JbnB1dEhUTUxBdHRyaWJ1dGVzPEhUTUxJbnB1dEVsZW1lbnQ+IHt9XG5cbmNvbnN0IElucHV0ID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MSW5wdXRFbGVtZW50LCBJbnB1dFByb3BzPihcbiAgKHsgY2xhc3NOYW1lLCB0eXBlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgICByZXR1cm4gKFxuICAgICAgPGlucHV0XG4gICAgICAgIHR5cGU9e3R5cGV9XG4gICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgXCJmbGV4IGgtMTAgdy1mdWxsIHJvdW5kZWQtbWQgYm9yZGVyIGJvcmRlci1pbnB1dCBiZy1iYWNrZ3JvdW5kIHB4LTMgcHktMiB0ZXh0LXNtIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTBcIixcbiAgICAgICAgICBjbGFzc05hbWVcbiAgICAgICAgKX1cbiAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgIHsuLi5wcm9wc31cbiAgICAgIC8+XG4gICAgKVxuICB9XG4pXG5JbnB1dC5kaXNwbGF5TmFtZSA9IFwiSW5wdXRcIlxuXG5leHBvcnQgeyBJbnB1dCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIklucHV0IiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInR5cGUiLCJwcm9wcyIsInJlZiIsImlucHV0IiwiZGlzcGxheU5hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./constants/routes.ts":
/*!*****************************!*\
  !*** ./constants/routes.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NAV_ITEMS: () => (/* binding */ NAV_ITEMS),\n/* harmony export */   ROUTES: () => (/* binding */ ROUTES)\n/* harmony export */ });\nconst ROUTES = {\n    HOME: '/',\n    LOGIN: '/login',\n    DASHBOARD: '/dashboard',\n    // Project Management\n    PROJECTS: '/projects',\n    PROJECT_DETAIL: (id)=>`/projects/${id}`,\n    PROJECT_CREATE: '/projects/create',\n    PROJECT_EDIT: (id)=>`/projects/${id}/edit`,\n    // Dataset Management\n    DATASETS: '/datasets',\n    DATASET_DETAIL: (id)=>`/datasets/${id}`,\n    DATASET_CREATE: '/datasets/create',\n    DATASET_EDIT: (id)=>`/datasets/${id}/edit`,\n    // Model Management\n    MODELS: '/models',\n    MODEL_DETAIL: (id)=>`/models/${id}`,\n    MODEL_CREATE: '/models/create',\n    MODEL_EDIT: (id)=>`/models/${id}/edit`,\n    // Annotation Management\n    ANNOTATIONS: '/annotations',\n    ANNOTATION_DETAIL: (id)=>`/annotations/${id}`,\n    ANNOTATION_CREATE: '/annotations/create',\n    ANNOTATION_EDIT: (id)=>`/annotations/${id}/edit`,\n    // Task Management\n    TASKS: '/tasks',\n    TASK_DETAIL: (id)=>`/tasks/${id}`,\n    TASK_CREATE: '/tasks/create',\n    TASK_EDIT: (id)=>`/tasks/${id}/edit`,\n    // User Management\n    PROFILE: '/profile',\n    SETTINGS: '/settings'\n};\nconst NAV_ITEMS = [\n    {\n        title: '项目管理',\n        href: ROUTES.PROJECTS,\n        children: [\n            {\n                title: '项目列表',\n                href: ROUTES.PROJECTS\n            },\n            {\n                title: '项目创建',\n                href: ROUTES.PROJECT_CREATE\n            }\n        ]\n    },\n    {\n        title: '数据集管理',\n        href: ROUTES.DATASETS,\n        children: [\n            {\n                title: '数据集列表',\n                href: ROUTES.DATASETS\n            },\n            {\n                title: '数据集创建',\n                href: ROUTES.DATASET_CREATE\n            }\n        ]\n    },\n    {\n        title: '模型管理',\n        href: ROUTES.MODELS,\n        children: [\n            {\n                title: '模型列表',\n                href: ROUTES.MODELS\n            },\n            {\n                title: '模型创建',\n                href: ROUTES.MODEL_CREATE\n            }\n        ]\n    },\n    {\n        title: '标注管理',\n        href: ROUTES.ANNOTATIONS,\n        children: [\n            {\n                title: '标注列表',\n                href: ROUTES.ANNOTATIONS\n            },\n            {\n                title: '标注创建',\n                href: ROUTES.ANNOTATION_CREATE\n            }\n        ]\n    },\n    {\n        title: '任务管理',\n        href: ROUTES.TASKS,\n        children: [\n            {\n                title: '任务列表',\n                href: ROUTES.TASKS\n            },\n            {\n                title: '任务创建',\n                href: ROUTES.TASK_CREATE\n            }\n        ]\n    },\n    {\n        title: '个人中心',\n        href: ROUTES.PROFILE\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb25zdGFudHMvcm91dGVzLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQU8sTUFBTUEsU0FBUztJQUNwQkMsTUFBTTtJQUNOQyxPQUFPO0lBQ1BDLFdBQVc7SUFFWCxxQkFBcUI7SUFDckJDLFVBQVU7SUFDVkMsZ0JBQWdCLENBQUNDLEtBQWUsQ0FBQyxVQUFVLEVBQUVBLElBQUk7SUFDakRDLGdCQUFnQjtJQUNoQkMsY0FBYyxDQUFDRixLQUFlLENBQUMsVUFBVSxFQUFFQSxHQUFHLEtBQUssQ0FBQztJQUVwRCxxQkFBcUI7SUFDckJHLFVBQVU7SUFDVkMsZ0JBQWdCLENBQUNKLEtBQWUsQ0FBQyxVQUFVLEVBQUVBLElBQUk7SUFDakRLLGdCQUFnQjtJQUNoQkMsY0FBYyxDQUFDTixLQUFlLENBQUMsVUFBVSxFQUFFQSxHQUFHLEtBQUssQ0FBQztJQUVwRCxtQkFBbUI7SUFDbkJPLFFBQVE7SUFDUkMsY0FBYyxDQUFDUixLQUFlLENBQUMsUUFBUSxFQUFFQSxJQUFJO0lBQzdDUyxjQUFjO0lBQ2RDLFlBQVksQ0FBQ1YsS0FBZSxDQUFDLFFBQVEsRUFBRUEsR0FBRyxLQUFLLENBQUM7SUFFaEQsd0JBQXdCO0lBQ3hCVyxhQUFhO0lBQ2JDLG1CQUFtQixDQUFDWixLQUFlLENBQUMsYUFBYSxFQUFFQSxJQUFJO0lBQ3ZEYSxtQkFBbUI7SUFDbkJDLGlCQUFpQixDQUFDZCxLQUFlLENBQUMsYUFBYSxFQUFFQSxHQUFHLEtBQUssQ0FBQztJQUUxRCxrQkFBa0I7SUFDbEJlLE9BQU87SUFDUEMsYUFBYSxDQUFDaEIsS0FBZSxDQUFDLE9BQU8sRUFBRUEsSUFBSTtJQUMzQ2lCLGFBQWE7SUFDYkMsV0FBVyxDQUFDbEIsS0FBZSxDQUFDLE9BQU8sRUFBRUEsR0FBRyxLQUFLLENBQUM7SUFFOUMsa0JBQWtCO0lBQ2xCbUIsU0FBUztJQUNUQyxVQUFVO0FBQ1osRUFBVztBQUVKLE1BQU1DLFlBQVk7SUFDdkI7UUFDRUMsT0FBTztRQUNQQyxNQUFNN0IsT0FBT0ksUUFBUTtRQUNyQjBCLFVBQVU7WUFDUjtnQkFBRUYsT0FBTztnQkFBUUMsTUFBTTdCLE9BQU9JLFFBQVE7WUFBQztZQUN2QztnQkFBRXdCLE9BQU87Z0JBQVFDLE1BQU03QixPQUFPTyxjQUFjO1lBQUM7U0FDOUM7SUFDSDtJQUNBO1FBQ0VxQixPQUFPO1FBQ1BDLE1BQU03QixPQUFPUyxRQUFRO1FBQ3JCcUIsVUFBVTtZQUNSO2dCQUFFRixPQUFPO2dCQUFTQyxNQUFNN0IsT0FBT1MsUUFBUTtZQUFDO1lBQ3hDO2dCQUFFbUIsT0FBTztnQkFBU0MsTUFBTTdCLE9BQU9XLGNBQWM7WUFBQztTQUMvQztJQUNIO0lBQ0E7UUFDRWlCLE9BQU87UUFDUEMsTUFBTTdCLE9BQU9hLE1BQU07UUFDbkJpQixVQUFVO1lBQ1I7Z0JBQUVGLE9BQU87Z0JBQVFDLE1BQU03QixPQUFPYSxNQUFNO1lBQUM7WUFDckM7Z0JBQUVlLE9BQU87Z0JBQVFDLE1BQU03QixPQUFPZSxZQUFZO1lBQUM7U0FDNUM7SUFDSDtJQUNBO1FBQ0VhLE9BQU87UUFDUEMsTUFBTTdCLE9BQU9pQixXQUFXO1FBQ3hCYSxVQUFVO1lBQ1I7Z0JBQUVGLE9BQU87Z0JBQVFDLE1BQU03QixPQUFPaUIsV0FBVztZQUFDO1lBQzFDO2dCQUFFVyxPQUFPO2dCQUFRQyxNQUFNN0IsT0FBT21CLGlCQUFpQjtZQUFDO1NBQ2pEO0lBQ0g7SUFDQTtRQUNFUyxPQUFPO1FBQ1BDLE1BQU03QixPQUFPcUIsS0FBSztRQUNsQlMsVUFBVTtZQUNSO2dCQUFFRixPQUFPO2dCQUFRQyxNQUFNN0IsT0FBT3FCLEtBQUs7WUFBQztZQUNwQztnQkFBRU8sT0FBTztnQkFBUUMsTUFBTTdCLE9BQU91QixXQUFXO1lBQUM7U0FDM0M7SUFDSDtJQUNBO1FBQ0VLLE9BQU87UUFDUEMsTUFBTTdCLE9BQU95QixPQUFPO0lBQ3RCO0NBQ0QsQ0FBVSIsInNvdXJjZXMiOlsiRjpcXGRlZXBzaWdodFxcZnJvbmV0ZW5kXFxjb25zdGFudHNcXHJvdXRlcy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgUk9VVEVTID0ge1xuICBIT01FOiAnLycsXG4gIExPR0lOOiAnL2xvZ2luJyxcbiAgREFTSEJPQVJEOiAnL2Rhc2hib2FyZCcsXG4gIFxuICAvLyBQcm9qZWN0IE1hbmFnZW1lbnRcbiAgUFJPSkVDVFM6ICcvcHJvamVjdHMnLFxuICBQUk9KRUNUX0RFVEFJTDogKGlkOiBzdHJpbmcpID0+IGAvcHJvamVjdHMvJHtpZH1gLFxuICBQUk9KRUNUX0NSRUFURTogJy9wcm9qZWN0cy9jcmVhdGUnLFxuICBQUk9KRUNUX0VESVQ6IChpZDogc3RyaW5nKSA9PiBgL3Byb2plY3RzLyR7aWR9L2VkaXRgLFxuICBcbiAgLy8gRGF0YXNldCBNYW5hZ2VtZW50XG4gIERBVEFTRVRTOiAnL2RhdGFzZXRzJyxcbiAgREFUQVNFVF9ERVRBSUw6IChpZDogc3RyaW5nKSA9PiBgL2RhdGFzZXRzLyR7aWR9YCxcbiAgREFUQVNFVF9DUkVBVEU6ICcvZGF0YXNldHMvY3JlYXRlJyxcbiAgREFUQVNFVF9FRElUOiAoaWQ6IHN0cmluZykgPT4gYC9kYXRhc2V0cy8ke2lkfS9lZGl0YCxcbiAgXG4gIC8vIE1vZGVsIE1hbmFnZW1lbnRcbiAgTU9ERUxTOiAnL21vZGVscycsXG4gIE1PREVMX0RFVEFJTDogKGlkOiBzdHJpbmcpID0+IGAvbW9kZWxzLyR7aWR9YCxcbiAgTU9ERUxfQ1JFQVRFOiAnL21vZGVscy9jcmVhdGUnLFxuICBNT0RFTF9FRElUOiAoaWQ6IHN0cmluZykgPT4gYC9tb2RlbHMvJHtpZH0vZWRpdGAsXG4gIFxuICAvLyBBbm5vdGF0aW9uIE1hbmFnZW1lbnRcbiAgQU5OT1RBVElPTlM6ICcvYW5ub3RhdGlvbnMnLFxuICBBTk5PVEFUSU9OX0RFVEFJTDogKGlkOiBzdHJpbmcpID0+IGAvYW5ub3RhdGlvbnMvJHtpZH1gLFxuICBBTk5PVEFUSU9OX0NSRUFURTogJy9hbm5vdGF0aW9ucy9jcmVhdGUnLFxuICBBTk5PVEFUSU9OX0VESVQ6IChpZDogc3RyaW5nKSA9PiBgL2Fubm90YXRpb25zLyR7aWR9L2VkaXRgLFxuICBcbiAgLy8gVGFzayBNYW5hZ2VtZW50XG4gIFRBU0tTOiAnL3Rhc2tzJyxcbiAgVEFTS19ERVRBSUw6IChpZDogc3RyaW5nKSA9PiBgL3Rhc2tzLyR7aWR9YCxcbiAgVEFTS19DUkVBVEU6ICcvdGFza3MvY3JlYXRlJyxcbiAgVEFTS19FRElUOiAoaWQ6IHN0cmluZykgPT4gYC90YXNrcy8ke2lkfS9lZGl0YCxcbiAgXG4gIC8vIFVzZXIgTWFuYWdlbWVudFxuICBQUk9GSUxFOiAnL3Byb2ZpbGUnLFxuICBTRVRUSU5HUzogJy9zZXR0aW5ncycsXG59IGFzIGNvbnN0O1xuXG5leHBvcnQgY29uc3QgTkFWX0lURU1TID0gW1xuICB7XG4gICAgdGl0bGU6ICfpobnnm67nrqHnkIYnLFxuICAgIGhyZWY6IFJPVVRFUy5QUk9KRUNUUyxcbiAgICBjaGlsZHJlbjogW1xuICAgICAgeyB0aXRsZTogJ+mhueebruWIl+ihqCcsIGhyZWY6IFJPVVRFUy5QUk9KRUNUUyB9LFxuICAgICAgeyB0aXRsZTogJ+mhueebruWIm+W7uicsIGhyZWY6IFJPVVRFUy5QUk9KRUNUX0NSRUFURSB9LFxuICAgIF0sXG4gIH0sXG4gIHtcbiAgICB0aXRsZTogJ+aVsOaNrumbhueuoeeQhicsXG4gICAgaHJlZjogUk9VVEVTLkRBVEFTRVRTLFxuICAgIGNoaWxkcmVuOiBbXG4gICAgICB7IHRpdGxlOiAn5pWw5o2u6ZuG5YiX6KGoJywgaHJlZjogUk9VVEVTLkRBVEFTRVRTIH0sXG4gICAgICB7IHRpdGxlOiAn5pWw5o2u6ZuG5Yib5bu6JywgaHJlZjogUk9VVEVTLkRBVEFTRVRfQ1JFQVRFIH0sXG4gICAgXSxcbiAgfSxcbiAge1xuICAgIHRpdGxlOiAn5qih5Z6L566h55CGJyxcbiAgICBocmVmOiBST1VURVMuTU9ERUxTLFxuICAgIGNoaWxkcmVuOiBbXG4gICAgICB7IHRpdGxlOiAn5qih5Z6L5YiX6KGoJywgaHJlZjogUk9VVEVTLk1PREVMUyB9LFxuICAgICAgeyB0aXRsZTogJ+aooeWei+WIm+W7uicsIGhyZWY6IFJPVVRFUy5NT0RFTF9DUkVBVEUgfSxcbiAgICBdLFxuICB9LFxuICB7XG4gICAgdGl0bGU6ICfmoIfms6jnrqHnkIYnLFxuICAgIGhyZWY6IFJPVVRFUy5BTk5PVEFUSU9OUyxcbiAgICBjaGlsZHJlbjogW1xuICAgICAgeyB0aXRsZTogJ+agh+azqOWIl+ihqCcsIGhyZWY6IFJPVVRFUy5BTk5PVEFUSU9OUyB9LFxuICAgICAgeyB0aXRsZTogJ+agh+azqOWIm+W7uicsIGhyZWY6IFJPVVRFUy5BTk5PVEFUSU9OX0NSRUFURSB9LFxuICAgIF0sXG4gIH0sXG4gIHtcbiAgICB0aXRsZTogJ+S7u+WKoeeuoeeQhicsXG4gICAgaHJlZjogUk9VVEVTLlRBU0tTLFxuICAgIGNoaWxkcmVuOiBbXG4gICAgICB7IHRpdGxlOiAn5Lu75Yqh5YiX6KGoJywgaHJlZjogUk9VVEVTLlRBU0tTIH0sXG4gICAgICB7IHRpdGxlOiAn5Lu75Yqh5Yib5bu6JywgaHJlZjogUk9VVEVTLlRBU0tfQ1JFQVRFIH0sXG4gICAgXSxcbiAgfSxcbiAge1xuICAgIHRpdGxlOiAn5Liq5Lq65Lit5b+DJyxcbiAgICBocmVmOiBST1VURVMuUFJPRklMRSxcbiAgfSxcbl0gYXMgY29uc3Q7XG4iXSwibmFtZXMiOlsiUk9VVEVTIiwiSE9NRSIsIkxPR0lOIiwiREFTSEJPQVJEIiwiUFJPSkVDVFMiLCJQUk9KRUNUX0RFVEFJTCIsImlkIiwiUFJPSkVDVF9DUkVBVEUiLCJQUk9KRUNUX0VESVQiLCJEQVRBU0VUUyIsIkRBVEFTRVRfREVUQUlMIiwiREFUQVNFVF9DUkVBVEUiLCJEQVRBU0VUX0VESVQiLCJNT0RFTFMiLCJNT0RFTF9ERVRBSUwiLCJNT0RFTF9DUkVBVEUiLCJNT0RFTF9FRElUIiwiQU5OT1RBVElPTlMiLCJBTk5PVEFUSU9OX0RFVEFJTCIsIkFOTk9UQVRJT05fQ1JFQVRFIiwiQU5OT1RBVElPTl9FRElUIiwiVEFTS1MiLCJUQVNLX0RFVEFJTCIsIlRBU0tfQ1JFQVRFIiwiVEFTS19FRElUIiwiUFJPRklMRSIsIlNFVFRJTkdTIiwiTkFWX0lURU1TIiwidGl0bGUiLCJocmVmIiwiY2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./constants/routes.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJGOlxcZGVlcHNpZ2h0XFxmcm9uZXRlbmRcXGxpYlxcdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Capp%5C%5Cannotations%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Capp%5C%5Cannotations%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/annotations/page.tsx */ \"(ssr)/./app/annotations/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNkZWVwc2lnaHQlNUMlNUNmcm9uZXRlbmQlNUMlNUNhcHAlNUMlNUNhbm5vdGF0aW9ucyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnS0FBeUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkY6XFxcXGRlZXBzaWdodFxcXFxmcm9uZXRlbmRcXFxcYXBwXFxcXGFubm90YXRpb25zXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Capp%5C%5Cannotations%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/error.tsx */ \"(ssr)/./app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNkZWVwc2lnaHQlNUMlNUNmcm9uZXRlbmQlNUMlNUNhcHAlNUMlNUNlcnJvci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBJQUE2RSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRjpcXFxcZGVlcHNpZ2h0XFxcXGZyb25ldGVuZFxcXFxhcHBcXFxcZXJyb3IudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Ccomponents%5C%5Cui%5C%5Cbutton.tsx%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Ccomponents%5C%5Cui%5C%5Ccard.tsx%22%2C%22ids%22%3A%5B%22Card%22%2C%22CardHeader%22%2C%22CardTitle%22%2C%22CardDescription%22%2C%22CardContent%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Ccomponents%5C%5Cui%5C%5Cbutton.tsx%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Ccomponents%5C%5Cui%5C%5Ccard.tsx%22%2C%22ids%22%3A%5B%22Card%22%2C%22CardHeader%22%2C%22CardTitle%22%2C%22CardDescription%22%2C%22CardContent%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/button.tsx */ \"(ssr)/./components/ui/button.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/card.tsx */ \"(ssr)/./components/ui/card.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNkZWVwc2lnaHQlNUMlNUNmcm9uZXRlbmQlNUMlNUNjb21wb25lbnRzJTVDJTVDdWklNUMlNUNidXR0b24udHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQnV0dG9uJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNkZWVwc2lnaHQlNUMlNUNmcm9uZXRlbmQlNUMlNUNjb21wb25lbnRzJTVDJTVDdWklNUMlNUNjYXJkLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkNhcmQlMjIlMkMlMjJDYXJkSGVhZGVyJTIyJTJDJTIyQ2FyZFRpdGxlJTIyJTJDJTIyQ2FyZERlc2NyaXB0aW9uJTIyJTJDJTIyQ2FyZENvbnRlbnQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRiUzQSU1QyU1Q2RlZXBzaWdodCU1QyU1Q2Zyb25ldGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDYXBwLWRpciU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0tBQXFIO0FBQ3JIO0FBQ0EsNEpBQTBLO0FBQzFLO0FBQ0EsZ05BQTJKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJCdXR0b25cIl0gKi8gXCJGOlxcXFxkZWVwc2lnaHRcXFxcZnJvbmV0ZW5kXFxcXGNvbXBvbmVudHNcXFxcdWlcXFxcYnV0dG9uLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQ2FyZFwiLFwiQ2FyZEhlYWRlclwiLFwiQ2FyZFRpdGxlXCIsXCJDYXJkRGVzY3JpcHRpb25cIixcIkNhcmRDb250ZW50XCJdICovIFwiRjpcXFxcZGVlcHNpZ2h0XFxcXGZyb25ldGVuZFxcXFxjb21wb25lbnRzXFxcXHVpXFxcXGNhcmQudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJfX2VzTW9kdWxlXCIsXCJkZWZhdWx0XCJdICovIFwiRjpcXFxcZGVlcHNpZ2h0XFxcXGZyb25ldGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxhcHAtZGlyXFxcXGxpbmsuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Ccomponents%5C%5Cui%5C%5Cbutton.tsx%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Ccomponents%5C%5Cui%5C%5Ccard.tsx%22%2C%22ids%22%3A%5B%22Card%22%2C%22CardHeader%22%2C%22CardTitle%22%2C%22CardDescription%22%2C%22CardContent%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cdeepsight%5C%5Cfronetend%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./services/mockData.ts":
/*!******************************!*\
  !*** ./services/mockData.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAnnotationsByTaskId: () => (/* binding */ getAnnotationsByTaskId),\n/* harmony export */   getAnnotationsByUserId: () => (/* binding */ getAnnotationsByUserId),\n/* harmony export */   getDatasetById: () => (/* binding */ getDatasetById),\n/* harmony export */   getDatasetsByProjectId: () => (/* binding */ getDatasetsByProjectId),\n/* harmony export */   getModelById: () => (/* binding */ getModelById),\n/* harmony export */   getProjectById: () => (/* binding */ getProjectById),\n/* harmony export */   getProjectsByUserId: () => (/* binding */ getProjectsByUserId),\n/* harmony export */   getTaskById: () => (/* binding */ getTaskById),\n/* harmony export */   getTasksByProjectId: () => (/* binding */ getTasksByProjectId),\n/* harmony export */   getTasksByUserId: () => (/* binding */ getTasksByUserId),\n/* harmony export */   getUserById: () => (/* binding */ getUserById),\n/* harmony export */   mockAnnotations: () => (/* binding */ mockAnnotations),\n/* harmony export */   mockDatasets: () => (/* binding */ mockDatasets),\n/* harmony export */   mockModels: () => (/* binding */ mockModels),\n/* harmony export */   mockProjects: () => (/* binding */ mockProjects),\n/* harmony export */   mockTasks: () => (/* binding */ mockTasks),\n/* harmony export */   mockUsers: () => (/* binding */ mockUsers)\n/* harmony export */ });\n// Mock Users\nconst mockUsers = [\n    {\n        id: '1',\n        username: 'admin',\n        email: '<EMAIL>',\n        avatar: '',\n        role: 'ADMIN',\n        createdAt: new Date('2023-01-15'),\n        updatedAt: new Date('2024-01-20')\n    },\n    {\n        id: '2',\n        username: 'zhangsan',\n        email: '<EMAIL>',\n        avatar: '',\n        role: 'ANNOTATOR',\n        createdAt: new Date('2023-02-01'),\n        updatedAt: new Date('2024-01-18')\n    },\n    {\n        id: '3',\n        username: 'lisi',\n        email: '<EMAIL>',\n        avatar: '',\n        role: 'MANAGER',\n        createdAt: new Date('2023-03-01'),\n        updatedAt: new Date('2024-01-15')\n    },\n    {\n        id: '4',\n        username: 'wangwu',\n        email: '<EMAIL>',\n        avatar: '',\n        role: 'ANNOTATOR',\n        createdAt: new Date('2023-04-01'),\n        updatedAt: new Date('2024-01-10')\n    }\n];\n// Mock Projects\nconst mockProjects = [\n    {\n        id: '1',\n        name: '图像分类项目',\n        description: '用于训练图像分类模型的数据标注项目，包含10个类别的图像数据',\n        status: 'ACTIVE',\n        createdBy: '1',\n        members: [\n            {\n                userId: '1',\n                role: 'OWNER',\n                joinedAt: new Date('2024-01-15')\n            },\n            {\n                userId: '2',\n                role: 'MEMBER',\n                joinedAt: new Date('2024-01-16')\n            },\n            {\n                userId: '3',\n                role: 'ADMIN',\n                joinedAt: new Date('2024-01-17')\n            }\n        ],\n        createdAt: new Date('2024-01-15'),\n        updatedAt: new Date('2024-01-20')\n    },\n    {\n        id: '2',\n        name: '目标检测项目',\n        description: '自动驾驶场景下的目标检测数据标注项目',\n        status: 'ACTIVE',\n        createdBy: '1',\n        members: [\n            {\n                userId: '1',\n                role: 'OWNER',\n                joinedAt: new Date('2024-01-10')\n            },\n            {\n                userId: '2',\n                role: 'MEMBER',\n                joinedAt: new Date('2024-01-11')\n            },\n            {\n                userId: '3',\n                role: 'MEMBER',\n                joinedAt: new Date('2024-01-12')\n            },\n            {\n                userId: '4',\n                role: 'MEMBER',\n                joinedAt: new Date('2024-01-13')\n            }\n        ],\n        createdAt: new Date('2024-01-10'),\n        updatedAt: new Date('2024-01-18')\n    },\n    {\n        id: '3',\n        name: '语音识别项目',\n        description: '多语言语音识别数据集标注项目',\n        status: 'COMPLETED',\n        createdBy: '3',\n        members: [\n            {\n                userId: '3',\n                role: 'OWNER',\n                joinedAt: new Date('2023-12-01')\n            },\n            {\n                userId: '4',\n                role: 'MEMBER',\n                joinedAt: new Date('2023-12-02')\n            }\n        ],\n        createdAt: new Date('2023-12-01'),\n        updatedAt: new Date('2024-01-05')\n    },\n    {\n        id: '4',\n        name: '文本分析项目',\n        description: '自然语言处理和情感分析项目',\n        status: 'PAUSED',\n        createdBy: '2',\n        members: [\n            {\n                userId: '2',\n                role: 'OWNER',\n                joinedAt: new Date('2024-01-05')\n            },\n            {\n                userId: '1',\n                role: 'ADMIN',\n                joinedAt: new Date('2024-01-06')\n            }\n        ],\n        createdAt: new Date('2024-01-05'),\n        updatedAt: new Date('2024-01-12')\n    }\n];\n// Mock Datasets\nconst mockDatasets = [\n    {\n        id: '1',\n        name: '图像分类数据集',\n        description: '包含10个类别的图像数据，用于训练分类模型',\n        type: 'IMAGE',\n        size: 10000,\n        status: 'READY',\n        projectId: '1',\n        createdBy: '1',\n        createdAt: new Date('2024-01-15'),\n        updatedAt: new Date('2024-01-20')\n    },\n    {\n        id: '2',\n        name: '目标检测数据集',\n        description: '自动驾驶场景下的目标检测数据',\n        type: 'IMAGE',\n        size: 5000,\n        status: 'PROCESSING',\n        projectId: '2',\n        createdBy: '1',\n        createdAt: new Date('2024-01-10'),\n        updatedAt: new Date('2024-01-18')\n    },\n    {\n        id: '3',\n        name: '语音识别数据集',\n        description: '多语言语音数据集，包含中英文语音',\n        type: 'AUDIO',\n        size: 2000,\n        status: 'READY',\n        projectId: '3',\n        createdBy: '3',\n        createdAt: new Date('2023-12-01'),\n        updatedAt: new Date('2024-01-05')\n    },\n    {\n        id: '4',\n        name: '文本情感数据集',\n        description: '用于情感分析的文本数据集',\n        type: 'TEXT',\n        size: 15000,\n        status: 'READY',\n        projectId: '4',\n        createdBy: '2',\n        createdAt: new Date('2024-01-05'),\n        updatedAt: new Date('2024-01-12')\n    },\n    {\n        id: '5',\n        name: '视频动作识别数据集',\n        description: '人体动作识别视频数据集',\n        type: 'VIDEO',\n        size: 800,\n        status: 'UPLOADING',\n        projectId: '1',\n        createdBy: '1',\n        createdAt: new Date('2024-01-18'),\n        updatedAt: new Date('2024-01-19')\n    }\n];\n// Mock Tasks\nconst mockTasks = [\n    {\n        id: '1',\n        name: '图像分类标注',\n        description: '对图像数据进行分类标注，包含10个类别',\n        type: 'CLASSIFICATION',\n        status: 'IN_PROGRESS',\n        projectId: '1',\n        datasetId: '1',\n        assignedTo: [\n            '2',\n            '3'\n        ],\n        createdBy: '1',\n        dueDate: new Date('2024-01-25'),\n        createdAt: new Date('2024-01-15'),\n        updatedAt: new Date('2024-01-20')\n    },\n    {\n        id: '2',\n        name: '目标检测标注',\n        description: '对自动驾驶场景进行目标检测标注',\n        type: 'DETECTION',\n        status: 'PENDING',\n        projectId: '2',\n        datasetId: '2',\n        assignedTo: [\n            '4'\n        ],\n        createdBy: '1',\n        dueDate: new Date('2024-01-30'),\n        createdAt: new Date('2024-01-10'),\n        updatedAt: new Date('2024-01-18')\n    },\n    {\n        id: '3',\n        name: '语音转录任务',\n        description: '将语音文件转录为文本',\n        type: 'TRANSCRIPTION',\n        status: 'COMPLETED',\n        projectId: '3',\n        datasetId: '3',\n        assignedTo: [\n            '4'\n        ],\n        createdBy: '3',\n        dueDate: new Date('2024-01-20'),\n        createdAt: new Date('2023-12-01'),\n        updatedAt: new Date('2024-01-05')\n    },\n    {\n        id: '4',\n        name: '情感分析标注',\n        description: '对文本进行情感极性标注',\n        type: 'CLASSIFICATION',\n        status: 'REVIEW',\n        projectId: '4',\n        datasetId: '4',\n        assignedTo: [\n            '2'\n        ],\n        createdBy: '2',\n        dueDate: new Date('2024-01-28'),\n        createdAt: new Date('2024-01-05'),\n        updatedAt: new Date('2024-01-12')\n    }\n];\n// Mock Annotations\nconst mockAnnotations = [\n    {\n        id: '1',\n        taskId: '1',\n        dataItemId: 'img_001',\n        annotatorId: '2',\n        data: {\n            category: '猫',\n            confidence: 0.95,\n            boundingBox: null\n        },\n        status: 'APPROVED',\n        createdAt: new Date('2024-01-20'),\n        updatedAt: new Date('2024-01-21')\n    },\n    {\n        id: '2',\n        taskId: '2',\n        dataItemId: 'img_002',\n        annotatorId: '3',\n        data: {\n            objects: [\n                {\n                    class: '汽车',\n                    bbox: [\n                        100,\n                        100,\n                        200,\n                        200\n                    ],\n                    confidence: 0.88\n                },\n                {\n                    class: '行人',\n                    bbox: [\n                        300,\n                        150,\n                        350,\n                        300\n                    ],\n                    confidence: 0.92\n                }\n            ]\n        },\n        status: 'SUBMITTED',\n        createdAt: new Date('2024-01-19'),\n        updatedAt: new Date('2024-01-20')\n    },\n    {\n        id: '3',\n        taskId: '3',\n        dataItemId: 'audio_001',\n        annotatorId: '4',\n        data: {\n            transcription: '你好，欢迎使用深眸AI标注平台',\n            language: 'zh-CN',\n            confidence: 0.96\n        },\n        status: 'APPROVED',\n        createdAt: new Date('2024-01-18'),\n        updatedAt: new Date('2024-01-19')\n    },\n    {\n        id: '4',\n        taskId: '4',\n        dataItemId: 'text_001',\n        annotatorId: '2',\n        data: {\n            sentiment: 'positive',\n            confidence: 0.87,\n            keywords: [\n                '优秀',\n                '满意',\n                '推荐'\n            ]\n        },\n        status: 'DRAFT',\n        createdAt: new Date('2024-01-17'),\n        updatedAt: new Date('2024-01-18')\n    }\n];\n// Mock Models\nconst mockModels = [\n    {\n        id: '1',\n        name: 'ResNet-50 分类模型',\n        description: '基于ResNet-50架构的图像分类模型，支持10个类别',\n        type: 'CLASSIFICATION',\n        version: 'v1.2.0',\n        status: 'DEPLOYED',\n        projectId: '1',\n        createdBy: '1',\n        createdAt: new Date('2024-01-15'),\n        updatedAt: new Date('2024-01-20')\n    },\n    {\n        id: '2',\n        name: 'YOLO-v8 检测模型',\n        description: '用于自动驾驶场景的目标检测模型',\n        type: 'DETECTION',\n        version: 'v2.1.0',\n        status: 'TRAINING',\n        projectId: '2',\n        createdBy: '1',\n        createdAt: new Date('2024-01-10'),\n        updatedAt: new Date('2024-01-18')\n    },\n    {\n        id: '3',\n        name: 'Whisper 语音识别',\n        description: '多语言语音识别模型，支持中英文',\n        type: 'NLP',\n        version: 'v1.0.0',\n        status: 'READY',\n        projectId: '3',\n        createdBy: '3',\n        createdAt: new Date('2023-12-01'),\n        updatedAt: new Date('2024-01-05')\n    },\n    {\n        id: '4',\n        name: 'BERT 情感分析模型',\n        description: '基于BERT的中文情感分析模型',\n        type: 'NLP',\n        version: 'v1.1.0',\n        status: 'READY',\n        projectId: '4',\n        createdBy: '2',\n        createdAt: new Date('2024-01-05'),\n        updatedAt: new Date('2024-01-12')\n    }\n];\n// Helper functions to get related data\nconst getUserById = (id)=>{\n    return mockUsers.find((user)=>user.id === id);\n};\nconst getProjectById = (id)=>{\n    return mockProjects.find((project)=>project.id === id);\n};\nconst getDatasetById = (id)=>{\n    return mockDatasets.find((dataset)=>dataset.id === id);\n};\nconst getTaskById = (id)=>{\n    return mockTasks.find((task)=>task.id === id);\n};\nconst getModelById = (id)=>{\n    return mockModels.find((model)=>model.id === id);\n};\nconst getProjectsByUserId = (userId)=>{\n    return mockProjects.filter((project)=>project.createdBy === userId || project.members.some((member)=>member.userId === userId));\n};\nconst getDatasetsByProjectId = (projectId)=>{\n    return mockDatasets.filter((dataset)=>dataset.projectId === projectId);\n};\nconst getTasksByProjectId = (projectId)=>{\n    return mockTasks.filter((task)=>task.projectId === projectId);\n};\nconst getTasksByUserId = (userId)=>{\n    return mockTasks.filter((task)=>task.assignedTo.includes(userId));\n};\nconst getAnnotationsByTaskId = (taskId)=>{\n    return mockAnnotations.filter((annotation)=>annotation.taskId === taskId);\n};\nconst getAnnotationsByUserId = (userId)=>{\n    return mockAnnotations.filter((annotation)=>annotation.annotatorId === userId);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./services/mockData.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/@radix-ui","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fannotations%2Fpage&page=%2Fannotations%2Fpage&appPaths=%2Fannotations%2Fpage&pagePath=private-next-app-dir%2Fannotations%2Fpage.tsx&appDir=F%3A%5Cdeepsight%5Cfronetend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Cdeepsight%5Cfronetend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();