'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ROUTES } from '@/constants/routes'

export default function LoginPage() {
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    // Simulate login process with mock data validation
    setTimeout(() => {
      // Mock user credentials
      const validCredentials = [
        { username: 'admin', password: 'admin123' },
        { username: 'z<PERSON><PERSON>', password: 'password' },
        { username: 'lisi', password: 'password' },
        { username: 'wangwu', password: 'password' },
      ]

      const isValid = validCredentials.some(
        cred => cred.username === username && cred.password === password
      )

      setIsLoading(false)

      if (isValid) {
        // Store user info in localStorage for demo
        localStorage.setItem('currentUser', username)
        localStorage.setItem('isAuthenticated', 'true')
        // Redirect to dashboard after successful login
        router.push(ROUTES.DASHBOARD)
      } else {
        alert('用户名或密码错误！\n\n有效账户：\n- admin / admin123\n- zhangsan / password\n- lisi / password\n- wangwu / password')
      }
    }, 1000)
  }

  const handleGitHubLogin = () => {
    // Implement GitHub OAuth login
    console.log('GitHub login clicked')
  }

  const handleGoogleLogin = () => {
    // Implement Google OAuth login
    console.log('Google login clicked')
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">深眸</h1>
          <p className="text-gray-600">AI 智能数据标注平台</p>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>登录</CardTitle>
            <CardDescription>
              请输入您的用户名和密码登录系统
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleLogin} className="space-y-4">
              <div>
                <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-1">
                  用户名
                </label>
                <Input
                  id="username"
                  type="text"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  placeholder="请输入用户名"
                  required
                />
              </div>
              
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                  密码
                </label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="请输入密码"
                  required
                />
              </div>
              
              <Button 
                type="submit" 
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? '登录中...' : '登录'}
              </Button>
            </form>
            
            <div className="mt-6">
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-white text-gray-500">或者使用第三方登录</span>
                </div>
              </div>
              
              <div className="mt-6 grid grid-cols-2 gap-3">
                <Button
                  variant="outline"
                  onClick={handleGitHubLogin}
                  className="w-full"
                >
                  GitHub
                </Button>
                <Button
                  variant="outline"
                  onClick={handleGoogleLogin}
                  className="w-full"
                >
                  Google
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
