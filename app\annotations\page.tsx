'use client'

import { useState } from 'react'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { ROUTES } from '@/constants/routes'
import { Plus, Search, MoreHorizontal, User, Calendar, Tag, CheckCircle } from 'lucide-react'

// Mock data
const mockAnnotations = [
  {
    id: '1',
    taskName: '图像分类标注',
    dataItemName: 'image_001.jpg',
    annotatorName: '张三',
    status: 'approved',
    confidence: 0.95,
    labels: ['猫', '动物'],
    createdAt: '2024-01-20',
    updatedAt: '2024-01-21',
  },
  {
    id: '2',
    taskName: '目标检测标注',
    dataItemName: 'image_002.jpg',
    annotatorName: '李四',
    status: 'submitted',
    confidence: 0.88,
    labels: ['汽车', '行人', '交通标志'],
    createdAt: '2024-01-19',
    updatedAt: '2024-01-20',
  },
  {
    id: '3',
    taskName: '语音转录',
    dataItemName: 'audio_001.wav',
    annotatorName: '王五',
    status: 'draft',
    confidence: 0.92,
    labels: ['中文', '对话'],
    createdAt: '2024-01-18',
    updatedAt: '2024-01-19',
  },
  {
    id: '4',
    taskName: '图像分割标注',
    dataItemName: 'image_003.jpg',
    annotatorName: '赵六',
    status: 'rejected',
    confidence: 0.76,
    labels: ['背景', '前景'],
    createdAt: '2024-01-17',
    updatedAt: '2024-01-18',
  },
]

const statusColors = {
  draft: 'bg-gray-100 text-gray-800',
  submitted: 'bg-blue-100 text-blue-800',
  approved: 'bg-green-100 text-green-800',
  rejected: 'bg-red-100 text-red-800',
}

const statusLabels = {
  draft: '草稿',
  submitted: '已提交',
  approved: '已批准',
  rejected: '已拒绝',
}

export default function AnnotationsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [annotations] = useState(mockAnnotations)

  const filteredAnnotations = annotations.filter(annotation =>
    annotation.taskName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    annotation.dataItemName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    annotation.annotatorName.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">标注管理</h1>
          <p className="text-gray-600 mt-2">管理您的数据标注结果</p>
        </div>
        <Link href={ROUTES.ANNOTATION_CREATE}>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            创建标注
          </Button>
        </Link>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="搜索标注..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Annotations Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredAnnotations.map((annotation) => (
          <Card key={annotation.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <CardTitle className="text-lg">{annotation.dataItemName}</CardTitle>
                  <CardDescription className="mt-2">
                    任务：{annotation.taskName}
                  </CardDescription>
                </div>
                <Button variant="ghost" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span
                    className={`px-2 py-1 text-xs rounded-full ${
                      statusColors[annotation.status as keyof typeof statusColors]
                    }`}
                  >
                    {statusLabels[annotation.status as keyof typeof statusLabels]}
                  </span>
                  <div className="flex items-center text-sm text-gray-500">
                    <CheckCircle className="h-4 w-4 mr-1" />
                    {Math.round(annotation.confidence * 100)}%
                  </div>
                </div>

                {/* Labels */}
                <div>
                  <div className="flex items-center text-sm text-gray-700 mb-2">
                    <Tag className="h-4 w-4 mr-1" />
                    标签
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {annotation.labels.map((label, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded"
                      >
                        {label}
                      </span>
                    ))}
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center text-sm text-gray-500">
                    <User className="h-4 w-4 mr-2" />
                    标注员：{annotation.annotatorName}
                  </div>
                  <div className="flex items-center text-sm text-gray-500">
                    <Calendar className="h-4 w-4 mr-2" />
                    更新：{annotation.updatedAt}
                  </div>
                </div>
                
                <div className="flex space-x-2">
                  <Link href={ROUTES.ANNOTATION_DETAIL(annotation.id)} className="flex-1">
                    <Button variant="outline" className="w-full">
                      查看详情
                    </Button>
                  </Link>
                  <Link href={ROUTES.ANNOTATION_EDIT(annotation.id)}>
                    <Button variant="ghost" size="icon">
                      编辑
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredAnnotations.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">没有找到匹配的标注</p>
        </div>
      )}
    </div>
  )
}
