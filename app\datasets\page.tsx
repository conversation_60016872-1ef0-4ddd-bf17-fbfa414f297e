'use client'

import { useState } from 'react'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { ROUTES } from '@/constants/routes'
import { Plus, Search, MoreHorizontal, Database, Calendar, FileText } from 'lucide-react'

// Mock data
const mockDatasets = [
  {
    id: '1',
    name: '图像分类数据集',
    description: '包含10个类别的图像数据，用于训练分类模型',
    type: 'image',
    size: 10000,
    status: 'ready',
    projectName: '图像分类项目',
    createdAt: '2024-01-15',
    updatedAt: '2024-01-20',
  },
  {
    id: '2',
    name: '目标检测数据集',
    description: '自动驾驶场景下的目标检测数据',
    type: 'image',
    size: 5000,
    status: 'processing',
    projectName: '目标检测项目',
    createdAt: '2024-01-10',
    updatedAt: '2024-01-18',
  },
  {
    id: '3',
    name: '语音识别数据集',
    description: '多语言语音数据集，包含中英文语音',
    type: 'audio',
    size: 2000,
    status: 'ready',
    projectName: '语音识别项目',
    createdAt: '2023-12-01',
    updatedAt: '2024-01-05',
  },
]

const statusColors = {
  uploading: 'bg-blue-100 text-blue-800',
  processing: 'bg-yellow-100 text-yellow-800',
  ready: 'bg-green-100 text-green-800',
  error: 'bg-red-100 text-red-800',
}

const statusLabels = {
  uploading: '上传中',
  processing: '处理中',
  ready: '就绪',
  error: '错误',
}

const typeLabels = {
  image: '图像',
  video: '视频',
  audio: '音频',
  text: '文本',
  multimodal: '多模态',
}

export default function DatasetsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [datasets] = useState(mockDatasets)

  const filteredDatasets = datasets.filter(dataset =>
    dataset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    dataset.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const formatFileSize = (size: number) => {
    if (size < 1000) return `${size} 个文件`
    if (size < 1000000) return `${(size / 1000).toFixed(1)}K 个文件`
    return `${(size / 1000000).toFixed(1)}M 个文件`
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">数据集管理</h1>
          <p className="text-gray-600 mt-2">管理您的训练数据集</p>
        </div>
        <Link href={ROUTES.DATASET_CREATE}>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            创建数据集
          </Button>
        </Link>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="搜索数据集..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Datasets Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredDatasets.map((dataset) => (
          <Card key={dataset.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <CardTitle className="text-lg">{dataset.name}</CardTitle>
                  <CardDescription className="mt-2">
                    {dataset.description}
                  </CardDescription>
                </div>
                <Button variant="ghost" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span
                    className={`px-2 py-1 text-xs rounded-full ${
                      statusColors[dataset.status as keyof typeof statusColors]
                    }`}
                  >
                    {statusLabels[dataset.status as keyof typeof statusLabels]}
                  </span>
                  <span className="text-xs text-gray-500">
                    {typeLabels[dataset.type as keyof typeof typeLabels]}
                  </span>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center text-sm text-gray-500">
                    <Database className="h-4 w-4 mr-2" />
                    {formatFileSize(dataset.size)}
                  </div>
                  <div className="flex items-center text-sm text-gray-500">
                    <FileText className="h-4 w-4 mr-2" />
                    {dataset.projectName}
                  </div>
                  <div className="flex items-center text-sm text-gray-500">
                    <Calendar className="h-4 w-4 mr-2" />
                    {dataset.updatedAt}
                  </div>
                </div>
                
                <div className="flex space-x-2">
                  <Link href={ROUTES.DATASET_DETAIL(dataset.id)} className="flex-1">
                    <Button variant="outline" className="w-full">
                      查看详情
                    </Button>
                  </Link>
                  <Link href={ROUTES.DATASET_EDIT(dataset.id)}>
                    <Button variant="ghost" size="icon">
                      编辑
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredDatasets.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">没有找到匹配的数据集</p>
        </div>
      )}
    </div>
  )
}
