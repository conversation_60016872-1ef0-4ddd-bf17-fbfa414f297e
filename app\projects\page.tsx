'use client'

import { useState } from 'react'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { ROUTES } from '@/constants/routes'
import { Plus, Search, MoreHorizontal, Users, Calendar } from 'lucide-react'

// Mock data
const mockProjects = [
  {
    id: '1',
    name: '图像分类项目',
    description: '用于训练图像分类模型的数据标注项目',
    status: 'active',
    memberCount: 5,
    createdAt: '2024-01-15',
    updatedAt: '2024-01-20',
  },
  {
    id: '2',
    name: '目标检测项目',
    description: '自动驾驶场景下的目标检测数据标注',
    status: 'paused',
    memberCount: 8,
    createdAt: '2024-01-10',
    updatedAt: '2024-01-18',
  },
  {
    id: '3',
    name: '语音识别项目',
    description: '多语言语音识别数据集标注项目',
    status: 'completed',
    memberCount: 3,
    createdAt: '2023-12-01',
    updatedAt: '2024-01-05',
  },
]

const statusColors = {
  active: 'bg-green-100 text-green-800',
  paused: 'bg-yellow-100 text-yellow-800',
  completed: 'bg-gray-100 text-gray-800',
  archived: 'bg-red-100 text-red-800',
}

const statusLabels = {
  active: '进行中',
  paused: '暂停',
  completed: '已完成',
  archived: '已归档',
}

export default function ProjectsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [projects] = useState(mockProjects)

  const filteredProjects = projects.filter(project =>
    project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    project.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">项目管理</h1>
          <p className="text-gray-600 mt-2">管理您的数据标注项目</p>
        </div>
        <Link href={ROUTES.PROJECT_CREATE}>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            创建项目
          </Button>
        </Link>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="搜索项目..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Projects Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredProjects.map((project) => (
          <Card key={project.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <CardTitle className="text-lg">{project.name}</CardTitle>
                  <CardDescription className="mt-2">
                    {project.description}
                  </CardDescription>
                </div>
                <Button variant="ghost" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span
                    className={`px-2 py-1 text-xs rounded-full ${
                      statusColors[project.status as keyof typeof statusColors]
                    }`}
                  >
                    {statusLabels[project.status as keyof typeof statusLabels]}
                  </span>
                </div>
                
                <div className="flex items-center text-sm text-gray-500 space-x-4">
                  <div className="flex items-center">
                    <Users className="h-4 w-4 mr-1" />
                    {project.memberCount} 成员
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1" />
                    {project.updatedAt}
                  </div>
                </div>
                
                <div className="flex space-x-2">
                  <Link href={ROUTES.PROJECT_DETAIL(project.id)} className="flex-1">
                    <Button variant="outline" className="w-full">
                      查看详情
                    </Button>
                  </Link>
                  <Link href={ROUTES.PROJECT_EDIT(project.id)}>
                    <Button variant="ghost" size="icon">
                      编辑
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredProjects.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">没有找到匹配的项目</p>
        </div>
      )}
    </div>
  )
}
