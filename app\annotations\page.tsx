'use client'

import { useState } from 'react'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { ROUTES } from '@/constants/routes'
import { Plus, Search, MoreHorizontal, User, Calendar, Tag, CheckCircle } from 'lucide-react'
import { mockAnnotations, getTaskById, getUserById } from '@/services/mockData'

const statusColors = {
  DRAFT: 'bg-gray-100 text-gray-800',
  SUBMITTED: 'bg-blue-100 text-blue-800',
  APPROVED: 'bg-green-100 text-green-800',
  REJECTED: 'bg-red-100 text-red-800',
}

const statusLabels = {
  DRAFT: '草稿',
  SUBMITTED: '已提交',
  APPROVED: '已批准',
  REJECTED: '已拒绝',
}

export default function AnnotationsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [annotations] = useState(mockAnnotations)

  const filteredAnnotations = annotations.filter(annotation => {
    const task = getTaskById(annotation.taskId)
    const annotator = getUserById(annotation.annotatorId)
    return (
      task?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      annotation.dataItemId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      annotator?.username.toLowerCase().includes(searchTerm.toLowerCase())
    )
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">标注管理</h1>
          <p className="text-gray-600 mt-2">管理您的数据标注结果</p>
        </div>
        <Link href={ROUTES.ANNOTATION_CREATE}>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            创建标注
          </Button>
        </Link>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="搜索标注..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Annotations Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredAnnotations.map((annotation) => {
          const task = getTaskById(annotation.taskId)
          const annotator = getUserById(annotation.annotatorId)

          // Extract labels from annotation data
          const getLabels = () => {
            if (annotation.data.category) return [annotation.data.category]
            if (annotation.data.objects) return annotation.data.objects.map((obj: any) => obj.class)
            if (annotation.data.sentiment) return [annotation.data.sentiment]
            if (annotation.data.transcription) return ['转录文本']
            return ['未知']
          }

          // Get confidence from annotation data
          const getConfidence = () => {
            if (annotation.data.confidence) return annotation.data.confidence
            if (annotation.data.objects) {
              const confidences = annotation.data.objects.map((obj: any) => obj.confidence)
              return confidences.reduce((a: number, b: number) => a + b, 0) / confidences.length
            }
            return 0.8 // default
          }

          return (
            <Card key={annotation.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <CardTitle className="text-lg">{annotation.dataItemId}</CardTitle>
                    <CardDescription className="mt-2">
                      任务：{task?.name}
                    </CardDescription>
                  </div>
                  <Button variant="ghost" size="icon">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span
                      className={`px-2 py-1 text-xs rounded-full ${
                        statusColors[annotation.status as keyof typeof statusColors]
                      }`}
                    >
                      {statusLabels[annotation.status as keyof typeof statusLabels]}
                    </span>
                    <div className="flex items-center text-sm text-gray-500">
                      <CheckCircle className="h-4 w-4 mr-1" />
                      {Math.round(getConfidence() * 100)}%
                    </div>
                  </div>

                  {/* Labels */}
                  <div>
                    <div className="flex items-center text-sm text-gray-700 mb-2">
                      <Tag className="h-4 w-4 mr-1" />
                      标签
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {getLabels().map((label, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded"
                        >
                          {label}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center text-sm text-gray-500">
                      <User className="h-4 w-4 mr-2" />
                      标注员：{annotator?.username}
                    </div>
                    <div className="flex items-center text-sm text-gray-500">
                      <Calendar className="h-4 w-4 mr-2" />
                      更新：{annotation.updatedAt.toLocaleDateString()}
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <Link href={ROUTES.ANNOTATION_DETAIL(annotation.id)} className="flex-1">
                      <Button variant="outline" className="w-full">
                        查看详情
                      </Button>
                    </Link>
                    <Link href={ROUTES.ANNOTATION_EDIT(annotation.id)}>
                      <Button variant="ghost" size="icon">
                        编辑
                      </Button>
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {filteredAnnotations.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">没有找到匹配的标注</p>
        </div>
      )}
    </div>
  )
}
