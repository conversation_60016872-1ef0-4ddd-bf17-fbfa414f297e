'use client'

import { useState } from 'react'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { ROUTES } from '@/constants/routes'
import { Plus, Search, MoreHorizontal, User, Calendar, Clock } from 'lucide-react'

// Mock data
const mockTasks = [
  {
    id: '1',
    name: '图像分类标注',
    description: '对图像数据进行分类标注，包含10个类别',
    type: 'classification',
    status: 'in_progress',
    projectName: '图像分类项目',
    assignedTo: ['张三', '李四'],
    dueDate: '2024-01-25',
    progress: 75,
    createdAt: '2024-01-15',
  },
  {
    id: '2',
    name: '目标检测标注',
    description: '对自动驾驶场景进行目标检测标注',
    type: 'detection',
    status: 'pending',
    projectName: '目标检测项目',
    assignedTo: ['王五'],
    dueDate: '2024-01-30',
    progress: 0,
    createdAt: '2024-01-10',
  },
  {
    id: '3',
    name: '语音转录任务',
    description: '将语音文件转录为文本',
    type: 'transcription',
    status: 'completed',
    projectName: '语音识别项目',
    assignedTo: ['赵六'],
    dueDate: '2024-01-20',
    progress: 100,
    createdAt: '2023-12-01',
  },
]

const statusColors = {
  pending: 'bg-gray-100 text-gray-800',
  in_progress: 'bg-blue-100 text-blue-800',
  review: 'bg-yellow-100 text-yellow-800',
  completed: 'bg-green-100 text-green-800',
  cancelled: 'bg-red-100 text-red-800',
}

const statusLabels = {
  pending: '待开始',
  in_progress: '进行中',
  review: '审核中',
  completed: '已完成',
  cancelled: '已取消',
}

const typeLabels = {
  classification: '分类',
  detection: '检测',
  segmentation: '分割',
  transcription: '转录',
  custom: '自定义',
}

export default function TasksPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [tasks] = useState(mockTasks)

  const filteredTasks = tasks.filter(task =>
    task.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    task.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const isOverdue = (dueDate: string) => {
    return new Date(dueDate) < new Date()
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">任务管理</h1>
          <p className="text-gray-600 mt-2">管理您的标注任务</p>
        </div>
        <Link href={ROUTES.TASK_CREATE}>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            创建任务
          </Button>
        </Link>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="搜索任务..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Tasks Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTasks.map((task) => (
          <Card key={task.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <CardTitle className="text-lg">{task.name}</CardTitle>
                  <CardDescription className="mt-2">
                    {task.description}
                  </CardDescription>
                </div>
                <Button variant="ghost" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span
                    className={`px-2 py-1 text-xs rounded-full ${
                      statusColors[task.status as keyof typeof statusColors]
                    }`}
                  >
                    {statusLabels[task.status as keyof typeof statusLabels]}
                  </span>
                  <span className="text-xs text-gray-500">
                    {typeLabels[task.type as keyof typeof typeLabels]}
                  </span>
                </div>

                {/* Progress Bar */}
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>进度</span>
                    <span>{task.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ width: `${task.progress}%` }}
                    ></div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center text-sm text-gray-500">
                    <User className="h-4 w-4 mr-2" />
                    {task.assignedTo.join(', ')}
                  </div>
                  <div className="flex items-center text-sm text-gray-500">
                    <Calendar className="h-4 w-4 mr-2" />
                    项目：{task.projectName}
                  </div>
                  <div className={`flex items-center text-sm ${
                    isOverdue(task.dueDate) && task.status !== 'completed' 
                      ? 'text-red-500' 
                      : 'text-gray-500'
                  }`}>
                    <Clock className="h-4 w-4 mr-2" />
                    截止：{task.dueDate}
                    {isOverdue(task.dueDate) && task.status !== 'completed' && (
                      <span className="ml-1 text-xs">(已逾期)</span>
                    )}
                  </div>
                </div>
                
                <div className="flex space-x-2">
                  <Link href={ROUTES.TASK_DETAIL(task.id)} className="flex-1">
                    <Button variant="outline" className="w-full">
                      查看详情
                    </Button>
                  </Link>
                  <Link href={ROUTES.TASK_EDIT(task.id)}>
                    <Button variant="ghost" size="icon">
                      编辑
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredTasks.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">没有找到匹配的任务</p>
        </div>
      )}
    </div>
  )
}
