'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { 
  MousePointer, 
  Square, 
  Circle, 
  MapPin,
  Polygon,
  Eye,
  EyeOff,
  ZoomIn,
  ZoomOut,
  RotateCw,
  Undo,
  Redo,
  Save,
  Trash2
} from 'lucide-react'

export type AnnotationTool = 'select' | 'rectangle' | 'circle' | 'polygon' | 'point'

interface AnnotationToolbarProps {
  currentTool: AnnotationTool
  onToolChange: (tool: AnnotationTool) => void
  currentLabel: string
  onLabelChange: (label: string) => void
  predefinedLabels: Array<{ name: string; color: string }>
  zoom: number
  onZoomChange: (zoom: number) => void
  showLabels: boolean
  onShowLabelsToggle: () => void
  canUndo: boolean
  canRedo: boolean
  onUndo: () => void
  onRedo: () => void
  onSave: () => void
  onClear: () => void
}

export function AnnotationToolbar({
  currentTool,
  onToolChange,
  currentLabel,
  onLabelChange,
  predefinedLabels,
  zoom,
  onZoomChange,
  showLabels,
  onShowLabelsToggle,
  canUndo,
  canRedo,
  onUndo,
  onRedo,
  onSave,
  onClear,
}: AnnotationToolbarProps) {
  const tools = [
    { id: 'select' as const, icon: MousePointer, label: '选择' },
    { id: 'rectangle' as const, icon: Square, label: '矩形' },
    { id: 'circle' as const, icon: Circle, label: '圆形' },
    { id: 'polygon' as const, icon: Polygon, label: '多边形' },
    { id: 'point' as const, icon: MapPin, label: '点标注' },
  ]

  return (
    <div className="space-y-4">
      {/* Tools */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">标注工具</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-2">
            {tools.map((tool) => {
              const Icon = tool.icon
              return (
                <Button
                  key={tool.id}
                  variant={currentTool === tool.id ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => onToolChange(tool.id)}
                  className="flex items-center justify-center"
                >
                  <Icon className="h-4 w-4 mr-1" />
                  {tool.label}
                </Button>
              )
            })}
          </div>

          {/* Label Selection */}
          <div>
            <label className="block text-sm font-medium mb-2">当前标签</label>
            <select
              value={currentLabel}
              onChange={(e) => onLabelChange(e.target.value)}
              className="w-full p-2 border rounded-md text-sm"
            >
              <option value="">选择标签</option>
              {predefinedLabels.map((label) => (
                <option key={label.name} value={label.name}>
                  {label.name}
                </option>
              ))}
            </select>
          </div>

          {/* Custom Label Input */}
          <div>
            <label className="block text-sm font-medium mb-2">自定义标签</label>
            <Input
              placeholder="输入自定义标签"
              value={currentLabel}
              onChange={(e) => onLabelChange(e.target.value)}
              className="text-sm"
            />
          </div>

          {/* Label Colors */}
          <div>
            <label className="block text-sm font-medium mb-2">标签颜色</label>
            <div className="flex flex-wrap gap-2">
              {predefinedLabels.map((label) => (
                <button
                  key={label.name}
                  className={`w-6 h-6 rounded border-2 ${
                    currentLabel === label.name ? 'border-gray-800' : 'border-gray-300'
                  }`}
                  style={{ backgroundColor: label.color }}
                  onClick={() => onLabelChange(label.name)}
                  title={label.name}
                />
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* View Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">视图控制</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Show/Hide Labels */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">显示标签</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={onShowLabelsToggle}
            >
              {showLabels ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
            </Button>
          </div>

          {/* Zoom Controls */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">缩放: {Math.round(zoom * 100)}%</span>
            </div>
            <div className="flex space-x-1">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onZoomChange(Math.max(0.1, zoom - 0.1))}
                disabled={zoom <= 0.1}
              >
                <ZoomOut className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onZoomChange(1)}
              >
                1:1
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onZoomChange(Math.min(5, zoom + 0.1))}
                disabled={zoom >= 5}
              >
                <ZoomIn className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">操作</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onUndo}
              disabled={!canUndo}
              className="flex-1"
            >
              <Undo className="h-4 w-4 mr-1" />
              撤销
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={onRedo}
              disabled={!canRedo}
              className="flex-1"
            >
              <Redo className="h-4 w-4 mr-1" />
              重做
            </Button>
          </div>
          
          <Button
            onClick={onSave}
            className="w-full"
          >
            <Save className="h-4 w-4 mr-2" />
            保存标注
          </Button>
          
          <Button
            variant="destructive"
            onClick={onClear}
            className="w-full"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            清空所有
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
